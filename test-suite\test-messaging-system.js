#!/usr/bin/env node

/**
 * Messaging System Feature Tests
 * Tests all real-time messaging functionality
 */

const { TestRunner, DEMO_USERS, BASE_URL } = require('./comprehensive-feature-test');

class MessagingSystemTests {
  constructor() {
    this.runner = new TestRunner();
  }

  async runAllTests() {
    console.log('\n💬 Starting Messaging System Tests...\n');

    try {
      // Authenticate users
      await this.runner.authenticate('clients', 0); // <PERSON>
      await this.runner.authenticate('clients', 1); // <PERSON>
      await this.runner.authenticate('vendors', 0); // <PERSON>
      await this.runner.authenticate('vendors', 1); // <PERSON>
      await this.runner.authenticate('admin');

      // Run all messaging system tests
      await this.testViewMessages();
      await this.testSendMessage();
      await this.testMessageWithAttachments();
      await this.testProjectConversations();
      await this.testGroupConversations();
      await this.testMessageDelivery();
      await this.testMessageSearch();
      await this.testConversationManagement();

    } catch (error) {
      console.error('Messaging test suite failed:', error);
    }

    return this.runner.generateReport();
  }

  async testViewMessages() {
    this.runner.startTest('View Messages', 'Test viewing existing messages and conversations');

    try {
      // Get all messages for client
      const clientMessages = await this.runner.makeRequest('GET', '/messages', null, 'clients', 0);
      
      if (!Array.isArray(clientMessages)) {
        throw new Error('Messages response is not an array');
      }

      this.runner.log(`Found ${clientMessages.length} messages for client`);

      // Test message structure
      if (clientMessages.length > 0) {
        const sampleMessage = clientMessages[0];
        const requiredFields = ['_id', 'sender', 'recipient', 'content', 'createdAt'];
        
        for (const field of requiredFields) {
          if (!(field in sampleMessage)) {
            throw new Error(`Message missing required field: ${field}`);
          }
        }

        this.runner.log('Message structure validation passed');
      }

      // Test conversations endpoint
      try {
        const conversations = await this.runner.makeRequest('GET', '/messages/conversations', null, 'clients', 0);
        
        if (Array.isArray(conversations)) {
          this.runner.log(`Found ${conversations.length} conversations`);
          
          // Verify conversation structure
          if (conversations.length > 0) {
            const sampleConversation = conversations[0];
            const convFields = ['_id', 'participants', 'lastMessage', 'updatedAt'];
            
            for (const field of convFields) {
              if (!(field in sampleConversation)) {
                this.runner.log(`Warning: Conversation missing field: ${field}`);
              }
            }
          }
        }
      } catch (convError) {
        this.runner.log('Conversations endpoint not implemented yet');
      }

      // Test vendor view
      const vendorMessages = await this.runner.makeRequest('GET', '/messages', null, 'vendors', 0);
      this.runner.log(`Vendor can see ${vendorMessages.length} messages`);

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testSendMessage() {
    this.runner.startTest('Send Message', 'Test sending messages between users');

    try {
      // Get recipient (vendor)
      const vendors = DEMO_USERS.vendors;
      const recipientEmail = vendors[0].email;

      // Send message from client to vendor
      const newMessage = {
        recipient: recipientEmail,
        subject: 'Project Discussion',
        content: 'Hi! I would like to discuss the project requirements with you. When would be a good time for a call?',
        type: 'direct'
      };

      const sentMessage = await this.runner.makeRequest('POST', '/messages', newMessage, 'clients', 0);
      
      if (!sentMessage._id) {
        throw new Error('Sent message does not have an ID');
      }

      this.runner.log(`Message sent successfully with ID: ${sentMessage._id}`);

      // Verify message was created correctly
      const fetchedMessage = await this.runner.makeRequest('GET', `/messages/${sentMessage._id}`, null, 'clients', 0);
      
      if (fetchedMessage.content !== newMessage.content) {
        throw new Error('Message content does not match');
      }

      this.runner.log('Message sending and verification successful');

      // Test reply functionality
      const replyMessage = {
        recipient: DEMO_USERS.clients[0].email,
        subject: 'Re: Project Discussion',
        content: 'Hello! I am available for a call tomorrow afternoon. Please let me know what time works best for you.',
        type: 'direct',
        replyTo: sentMessage._id
      };

      try {
        const reply = await this.runner.makeRequest('POST', '/messages', replyMessage, 'vendors', 0);
        this.runner.log(`Reply sent successfully with ID: ${reply._id}`);
      } catch (replyError) {
        this.runner.log('Reply functionality may not be fully implemented');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testMessageWithAttachments() {
    this.runner.startTest('Message with Attachments', 'Test sending messages with file attachments');

    try {
      const recipientEmail = DEMO_USERS.vendors[1].email;

      // Test message with attachment references
      const messageWithAttachments = {
        recipient: recipientEmail,
        subject: 'Project Requirements Document',
        content: 'Please find the detailed project requirements attached.',
        attachments: [
          {
            name: 'requirements.pdf',
            type: 'application/pdf',
            size: 1048576,
            url: '/uploads/requirements.pdf'
          },
          {
            name: 'wireframes.png',
            type: 'image/png',
            size: 2097152,
            url: '/uploads/wireframes.png'
          }
        ]
      };

      try {
        const messageWithFiles = await this.runner.makeRequest('POST', '/messages', messageWithAttachments, 'clients', 0);
        
        if (messageWithFiles._id) {
          this.runner.log('Message with attachments sent successfully');
          
          // Verify attachments are included
          if (messageWithFiles.attachments && messageWithFiles.attachments.length > 0) {
            this.runner.log(`Message includes ${messageWithFiles.attachments.length} attachments`);
          }
        }
      } catch (attachmentError) {
        this.runner.log('Message attachment functionality may not be fully implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testProjectConversations() {
    this.runner.startTest('Project Conversations', 'Test project-specific messaging');

    try {
      // Get a project
      const projects = await this.runner.makeRequest('GET', '/projects', null, 'clients', 0);
      
      if (projects.length === 0) {
        throw new Error('No projects available for conversation test');
      }

      const projectId = projects[0]._id;

      // Test project-specific message
      const projectMessage = {
        recipient: DEMO_USERS.vendors[0].email,
        subject: 'Project Update',
        content: 'I wanted to provide an update on the project timeline and discuss next steps.',
        type: 'project',
        project: projectId
      };

      try {
        const sentProjectMessage = await this.runner.makeRequest('POST', '/messages', projectMessage, 'clients', 0);
        this.runner.log(`Project message sent with ID: ${sentProjectMessage._id}`);
      } catch (projectMsgError) {
        this.runner.log('Project-specific messaging may not be fully implemented');
      }

      // Test getting messages for a specific project
      try {
        const projectMessages = await this.runner.makeRequest('GET', `/projects/${projectId}/messages`, null, 'clients', 0);
        
        if (Array.isArray(projectMessages)) {
          this.runner.log(`Found ${projectMessages.length} messages for project ${projectId}`);
        }
      } catch (projectMsgListError) {
        this.runner.log('Project message listing endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testGroupConversations() {
    this.runner.startTest('Group Conversations', 'Test group messaging for team collaboration');

    try {
      // Test creating a group conversation
      const groupData = {
        name: 'Project Team Discussion',
        description: 'Main communication channel for the development team',
        participants: [
          DEMO_USERS.clients[0].email,
          DEMO_USERS.vendors[0].email,
          DEMO_USERS.vendors[1].email
        ],
        type: 'group'
      };

      try {
        const groupConversation = await this.runner.makeRequest('POST', '/messages/groups', groupData, 'clients', 0);
        
        if (groupConversation._id) {
          this.runner.log(`Group conversation created with ID: ${groupConversation._id}`);
          
          // Test sending message to group
          const groupMessage = {
            conversation: groupConversation._id,
            content: 'Welcome everyone! Let\'s use this channel for all project-related discussions.',
            type: 'group'
          };

          const sentGroupMessage = await this.runner.makeRequest('POST', '/messages', groupMessage, 'clients', 0);
          this.runner.log(`Group message sent with ID: ${sentGroupMessage._id}`);
        }
      } catch (groupError) {
        this.runner.log('Group conversation functionality not implemented yet');
      }

      // Test group message visibility
      try {
        const groups = await this.runner.makeRequest('GET', '/messages/groups', null, 'vendors', 0);
        
        if (Array.isArray(groups)) {
          this.runner.log(`Vendor can see ${groups.length} group conversations`);
        }
      } catch (groupListError) {
        this.runner.log('Group listing endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testMessageDelivery() {
    this.runner.startTest('Message Delivery', 'Test real-time message delivery and read receipts');

    try {
      // Send a message and check delivery status
      const testMessage = {
        recipient: DEMO_USERS.vendors[0].email,
        subject: 'Delivery Test',
        content: 'This is a test message to verify delivery status.',
        requestReadReceipt: true
      };

      const sentMessage = await this.runner.makeRequest('POST', '/messages', testMessage, 'clients', 0);
      
      if (sentMessage._id) {
        this.runner.log(`Test message sent with ID: ${sentMessage._id}`);
        
        // Check delivery status
        try {
          const messageStatus = await this.runner.makeRequest('GET', `/messages/${sentMessage._id}/status`, null, 'clients', 0);
          
          if (messageStatus) {
            this.runner.log(`Message status: ${JSON.stringify(messageStatus)}`);
          }
        } catch (statusError) {
          this.runner.log('Message status endpoint not implemented yet');
        }

        // Test marking message as read (from recipient side)
        try {
          await this.runner.makeRequest('PUT', `/messages/${sentMessage._id}/read`, {}, 'vendors', 0);
          this.runner.log('Message marked as read successfully');
        } catch (readError) {
          this.runner.log('Read receipt functionality not implemented yet');
        }
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testMessageSearch() {
    this.runner.startTest('Message Search', 'Test searching messages by content and metadata');

    try {
      // Test message search functionality
      const searchTerms = ['project', 'requirements', 'timeline'];
      
      for (const term of searchTerms) {
        try {
          const searchResults = await this.runner.makeRequest('GET', `/messages?search=${term}`, null, 'clients', 0);
          
          if (Array.isArray(searchResults)) {
            this.runner.log(`Search for '${term}' returned ${searchResults.length} results`);
            
            // Verify search results contain the search term
            const relevantResults = searchResults.filter(m => 
              m.content.toLowerCase().includes(term.toLowerCase()) ||
              (m.subject && m.subject.toLowerCase().includes(term.toLowerCase()))
            );
            
            this.runner.log(`${relevantResults.length} results are relevant to search term '${term}'`);
          }
        } catch (searchError) {
          this.runner.log(`Message search for '${term}' not implemented or failed`);
        }
      }

      // Test filtering by date range
      try {
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
        const recentMessages = await this.runner.makeRequest('GET', `/messages?since=${oneWeekAgo}`, null, 'clients', 0);
        
        if (Array.isArray(recentMessages)) {
          this.runner.log(`Found ${recentMessages.length} messages from the last week`);
        }
      } catch (dateFilterError) {
        this.runner.log('Date filtering not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testConversationManagement() {
    this.runner.startTest('Conversation Management', 'Test conversation organization and management');

    try {
      // Test conversation listing
      try {
        const conversations = await this.runner.makeRequest('GET', '/messages/conversations', null, 'clients', 0);
        
        if (Array.isArray(conversations)) {
          this.runner.log(`Found ${conversations.length} conversations`);
          
          // Test conversation details
          if (conversations.length > 0) {
            const conversationId = conversations[0]._id;
            
            try {
              const conversationDetails = await this.runner.makeRequest('GET', `/messages/conversations/${conversationId}`, null, 'clients', 0);
              this.runner.log(`Conversation details loaded for: ${conversationId}`);
            } catch (detailsError) {
              this.runner.log('Conversation details endpoint not implemented yet');
            }
          }
        }
      } catch (convListError) {
        this.runner.log('Conversation listing not implemented yet');
      }

      // Test conversation archiving
      try {
        const conversations = await this.runner.makeRequest('GET', '/messages/conversations', null, 'clients', 0);
        
        if (conversations && conversations.length > 0) {
          const conversationId = conversations[0]._id;
          
          await this.runner.makeRequest('PUT', `/messages/conversations/${conversationId}/archive`, {}, 'clients', 0);
          this.runner.log(`Conversation ${conversationId} archived successfully`);
        }
      } catch (archiveError) {
        this.runner.log('Conversation archiving not implemented yet');
      }

      // Test unread message count
      try {
        const unreadCount = await this.runner.makeRequest('GET', '/messages/unread/count', null, 'vendors', 0);
        
        if (typeof unreadCount === 'object' && 'count' in unreadCount) {
          this.runner.log(`Vendor has ${unreadCount.count} unread messages`);
        }
      } catch (unreadError) {
        this.runner.log('Unread count endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tests = new MessagingSystemTests();
  tests.runAllTests().then(report => {
    console.log('\nMessaging System Tests Complete!');
    process.exit(report.summary.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = MessagingSystemTests;
