import React from 'react';
import { Shield, Lock, Eye, Server, CheckCircle, AlertTriangle } from 'lucide-react';

export default function Security() {
  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Shield className="w-16 h-16 mx-auto mb-6 text-primary-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Security & Trust
            </h1>
            <p className="text-xl text-primary-100 leading-relaxed">
              Your data security and privacy are our top priorities. Learn about our comprehensive security measures and compliance standards.
            </p>
          </div>
        </div>
      </div>

      {/* Security Features */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Enterprise-Grade Security
              </h2>
              <p className="text-lg text-secondary-600">
                We implement industry-leading security practices to protect your business
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Data Encryption */}
              <div className="card p-6">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                  <Lock className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  End-to-End Encryption
                </h3>
                <p className="text-secondary-600 mb-4">
                  All data is encrypted in transit and at rest using AES-256 encryption standards.
                </p>
                <ul className="space-y-2 text-sm text-secondary-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    TLS 1.3 for data in transit
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    AES-256 for data at rest
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Key rotation every 90 days
                  </li>
                </ul>
              </div>

              {/* Access Control */}
              <div className="card p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Eye className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Access Control
                </h3>
                <p className="text-secondary-600 mb-4">
                  Multi-factor authentication and role-based access controls protect your account.
                </p>
                <ul className="space-y-2 text-sm text-secondary-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Two-factor authentication
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Role-based permissions
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Session management
                  </li>
                </ul>
              </div>

              {/* Infrastructure */}
              <div className="card p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Server className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Secure Infrastructure
                </h3>
                <p className="text-secondary-600 mb-4">
                  Our infrastructure is hosted on secure, compliant cloud platforms.
                </p>
                <ul className="space-y-2 text-sm text-secondary-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    SOC 2 Type II certified
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    99.9% uptime SLA
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Regular security audits
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance */}
      <div className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-secondary-900 mb-6">
              Compliance & Certifications
            </h2>
            <p className="text-lg text-secondary-600 mb-12">
              We maintain the highest standards of compliance and security certifications
            </p>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="p-6 border border-secondary-200 rounded-lg">
                <h3 className="font-semibold text-secondary-900 mb-2">GDPR</h3>
                <p className="text-sm text-secondary-600">General Data Protection Regulation compliant</p>
              </div>
              <div className="p-6 border border-secondary-200 rounded-lg">
                <h3 className="font-semibold text-secondary-900 mb-2">SOC 2</h3>
                <p className="text-sm text-secondary-600">Type II security controls certification</p>
              </div>
              <div className="p-6 border border-secondary-200 rounded-lg">
                <h3 className="font-semibold text-secondary-900 mb-2">ISO 27001</h3>
                <p className="text-sm text-secondary-600">Information security management</p>
              </div>
              <div className="p-6 border border-secondary-200 rounded-lg">
                <h3 className="font-semibold text-secondary-900 mb-2">CCPA</h3>
                <p className="text-sm text-secondary-600">California Consumer Privacy Act compliant</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security Practices */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              Our Security Practices
            </h2>
            
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Regular Security Audits
                  </h3>
                  <p className="text-secondary-600">
                    We conduct quarterly security audits and penetration testing to identify and address potential vulnerabilities.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Employee Training
                  </h3>
                  <p className="text-secondary-600">
                    All employees undergo regular security training and follow strict security protocols to protect your data.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Incident Response
                  </h3>
                  <p className="text-secondary-600">
                    We have a comprehensive incident response plan and 24/7 monitoring to quickly address any security concerns.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact */}
      <div className="py-16 bg-secondary-900 text-white">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Security Questions?
            </h2>
            <p className="text-secondary-300 mb-8">
              Have questions about our security practices? Our security team is here to help.
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="btn-primary inline-flex items-center"
            >
              Contact Security Team
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
