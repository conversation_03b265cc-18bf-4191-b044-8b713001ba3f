import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Import models
import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import { Message, Conversation } from '../models/messageModel.js';
import Document from '../models/documentModel.js';
import Notification from '../models/notificationModel.js';

// Load environment variables
const __dirname = path.dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: path.join(__dirname, '../.env') });

// Demo data arrays
const demoUsers = [
  // Clients
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'client',
    company: 'TechCorp Solutions',
    companyLogo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
    bio: 'CEO of TechCorp Solutions, specializing in digital transformation for enterprises.',
    location: 'San Francisco, CA',
    website: 'https://techcorp.com',
    rating: 4.8
  },
  {
    name: 'Michael Chen',
    email: '<EMAIL>',
    password: 'password123',
    role: 'client',
    company: 'Innovate Digital',
    companyLogo: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=100&h=100&fit=crop&crop=center',
    bio: 'Founder of Innovate Digital, passionate about cutting-edge web technologies.',
    location: 'New York, NY',
    website: 'https://innovate.io',
    rating: 4.9
  },
  {
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    password: 'password123',
    role: 'client',
    company: 'NextGen Startup',
    companyLogo: 'https://images.unsplash.com/photo-1553484771-371a605b060b?w=100&h=100&fit=crop&crop=center',
    bio: 'Product Manager at NextGen Startup, focused on mobile-first solutions.',
    location: 'Austin, TX',
    website: 'https://nextgen-startup.com',
    rating: 4.7
  },
  
  // Vendors
  {
    name: 'Alex Thompson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'vendor',
    company: 'Thompson Web Solutions',
    companyLogo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center',
    bio: 'Full-stack developer with 8+ years experience in React, Node.js, and cloud technologies.',
    location: 'Seattle, WA',
    website: 'https://alexthompson.dev',
    skills: ['React', 'Node.js', 'MongoDB', 'AWS', 'TypeScript', 'GraphQL'],
    rating: 4.9
  },
  {
    name: 'Maria Garcia',
    email: '<EMAIL>',
    password: 'password123',
    role: 'vendor',
    company: 'Garcia Design Studio',
    companyLogo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center',
    bio: 'UI/UX Designer specializing in modern, user-centered design for web and mobile applications.',
    location: 'Los Angeles, CA',
    website: 'https://garciadesign.com',
    skills: ['UI/UX Design', 'Figma', 'Adobe Creative Suite', 'Prototyping', 'User Research'],
    rating: 4.8
  },
  {
    name: 'David Kim',
    email: '<EMAIL>',
    password: 'password123',
    role: 'vendor',
    company: 'Kim Mobile Solutions',
    companyLogo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center',
    bio: 'Mobile app developer with expertise in React Native, Flutter, and native iOS/Android development.',
    location: 'Chicago, IL',
    website: 'https://kimmobile.dev',
    skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Firebase', 'API Integration'],
    rating: 4.7
  },
  {
    name: 'Jennifer Wilson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'vendor',
    company: 'Wilson Digital Marketing',
    companyLogo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=center',
    bio: 'Digital marketing specialist with focus on SEO, content strategy, and social media marketing.',
    location: 'Miami, FL',
    website: 'https://wilsonmarketing.com',
    skills: ['SEO', 'Content Marketing', 'Social Media', 'Google Ads', 'Analytics', 'Email Marketing'],
    rating: 4.6
  },
  {
    name: 'Robert Brown',
    email: '<EMAIL>',
    password: 'password123',
    role: 'vendor',
    company: 'Brown Analytics',
    companyLogo: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=center',
    bio: 'Data scientist and analyst specializing in business intelligence and machine learning solutions.',
    location: 'Boston, MA',
    website: 'https://brownanalytics.com',
    skills: ['Python', 'R', 'SQL', 'Machine Learning', 'Tableau', 'Power BI', 'Statistics'],
    rating: 4.8
  },

  // Admin
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    company: 'GlobalConnect',
    companyLogo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=center',
    bio: 'Platform administrator for GlobalConnect.',
    location: 'Remote',
    website: 'https://globalconnect.com',
    rating: 5.0
  }
];

const demoProjects = [
  {
    title: 'E-commerce Website Development',
    description: 'Build a modern, responsive e-commerce website with React and Node.js. Features include user authentication, product catalog, shopping cart, payment integration, and admin dashboard. The site should be mobile-friendly and optimized for SEO.',
    budget: 15000,
    deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
    category: 'web-development',
    skills: ['React', 'Node.js', 'MongoDB', 'Stripe API', 'SEO'],
    visibility: 'public',
    status: 'open',
    attachments: []
  },
  {
    title: 'Mobile App UI/UX Design',
    description: 'Design a complete UI/UX for a fitness tracking mobile application. Need wireframes, mockups, and interactive prototypes. The design should be modern, intuitive, and follow iOS/Android design guidelines.',
    budget: 8000,
    deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    category: 'design',
    skills: ['UI/UX Design', 'Figma', 'Prototyping', 'Mobile Design'],
    visibility: 'public',
    status: 'in-progress',
    attachments: []
  },
  {
    title: 'SEO Optimization & Content Strategy',
    description: 'Comprehensive SEO audit and optimization for our corporate website. Includes keyword research, on-page optimization, content strategy, and monthly reporting. Goal is to improve organic search rankings and traffic.',
    budget: 5000,
    deadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
    category: 'marketing',
    skills: ['SEO', 'Content Marketing', 'Google Analytics', 'Keyword Research'],
    visibility: 'public',
    status: 'open',
    attachments: []
  },
  {
    title: 'React Native Mobile App Development',
    description: 'Develop a cross-platform mobile app using React Native for a food delivery service. Features include user registration, restaurant browsing, order placement, real-time tracking, and payment integration.',
    budget: 20000,
    deadline: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
    category: 'mobile-development',
    skills: ['React Native', 'Firebase', 'Google Maps API', 'Payment Integration'],
    visibility: 'public',
    status: 'open',
    attachments: []
  },
  {
    title: 'Data Analytics Dashboard',
    description: 'Create an interactive dashboard for business intelligence using Python and modern visualization tools. Dashboard should display KPIs, trends, and allow for data filtering and export functionality.',
    budget: 12000,
    deadline: new Date(Date.now() + 50 * 24 * 60 * 60 * 1000), // 50 days from now
    category: 'data-analytics',
    skills: ['Python', 'Tableau', 'SQL', 'Data Visualization', 'Business Intelligence'],
    visibility: 'public',
    status: 'review',
    attachments: []
  },
  {
    title: 'Content Writing for Tech Blog',
    description: 'Write 20 high-quality blog posts about emerging technologies, AI, and software development trends. Each post should be 1500-2000 words, SEO-optimized, and include relevant images.',
    budget: 3000,
    deadline: new Date(Date.now() + 40 * 24 * 60 * 60 * 1000), // 40 days from now
    category: 'content',
    skills: ['Technical Writing', 'SEO', 'Research', 'Content Strategy'],
    visibility: 'public',
    status: 'completed',
    attachments: []
  },
  {
    title: 'Enterprise CRM System',
    description: 'Develop a comprehensive CRM system for managing customer relationships, sales pipeline, and reporting. Should include user management, contact management, deal tracking, and integration capabilities.',
    budget: 35000,
    deadline: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000), // 120 days from now
    category: 'web-development',
    skills: ['React', 'Node.js', 'PostgreSQL', 'API Development', 'Authentication'],
    visibility: 'invite-only',
    status: 'open',
    attachments: []
  },
  {
    title: 'Brand Identity Design Package',
    description: 'Complete brand identity design including logo, color palette, typography, business cards, letterhead, and brand guidelines. Looking for a modern, professional design that reflects innovation.',
    budget: 6000,
    deadline: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000), // 25 days from now
    category: 'design',
    skills: ['Logo Design', 'Brand Identity', 'Adobe Illustrator', 'Print Design'],
    visibility: 'public',
    status: 'open',
    attachments: []
  }
];

// Function to hash passwords
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB Connected');
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
};

// Clear existing data
const clearData = async () => {
  try {
    await User.deleteMany({});
    await Project.deleteMany({});
    await Bid.deleteMany({});
    await Message.deleteMany({});
    await Conversation.deleteMany({});
    await Document.deleteMany({});
    await Notification.deleteMany({});
    console.log('✅ Existing data cleared');
  } catch (error) {
    console.error('❌ Error clearing data:', error);
  }
};

// Seed users
const seedUsers = async () => {
  try {
    const users = [];
    
    for (const userData of demoUsers) {
      const hashedPassword = await hashPassword(userData.password);
      users.push({
        ...userData,
        password: hashedPassword
      });
    }
    
    const createdUsers = await User.insertMany(users);
    console.log(`✅ Created ${createdUsers.length} users`);
    return createdUsers;
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    return [];
  }
};

// Seed projects
const seedProjects = async (users) => {
  try {
    const clients = users.filter(user => user.role === 'client');
    const projects = [];

    for (let i = 0; i < demoProjects.length; i++) {
      const projectData = demoProjects[i];
      const client = clients[i % clients.length];

      projects.push({
        ...projectData,
        client: client._id
      });
    }

    const createdProjects = await Project.insertMany(projects);
    console.log(`✅ Created ${createdProjects.length} projects`);
    return createdProjects;
  } catch (error) {
    console.error('❌ Error seeding projects:', error);
    return [];
  }
};

// Seed bids
const seedBids = async (projects, users) => {
  try {
    const vendors = users.filter(user => user.role === 'vendor');
    const bids = [];

    // Create multiple bids for each open project
    for (const project of projects) {
      if (project.status === 'open' || project.status === 'in-progress') {
        const numBids = Math.floor(Math.random() * 4) + 2; // 2-5 bids per project

        for (let i = 0; i < Math.min(numBids, vendors.length); i++) {
          const vendor = vendors[i];
          const baseAmount = project.budget;
          const bidAmount = Math.floor(baseAmount * (0.7 + Math.random() * 0.6)); // 70-130% of budget

          bids.push({
            project: project._id,
            vendor: vendor._id,
            amount: bidAmount,
            proposal: `I'm excited to work on "${project.title}". With my expertise in ${project.skills.slice(0, 3).join(', ')}, I can deliver high-quality results within your timeline. My approach includes thorough planning, regular communication, and iterative development to ensure your vision is realized.`,
            deliveryTime: Math.floor(Math.random() * 30) + 15, // 15-45 days
            status: i === 0 && project.status === 'in-progress' ? 'accepted' : 'pending',
            revisions: Math.floor(Math.random() * 3) + 1,
            attachments: []
          });
        }
      }
    }

    const createdBids = await Bid.insertMany(bids);
    console.log(`✅ Created ${createdBids.length} bids`);

    // Update projects with winning bids for in-progress projects
    for (const project of projects) {
      if (project.status === 'in-progress') {
        const acceptedBid = createdBids.find(bid =>
          bid.project.toString() === project._id.toString() && bid.status === 'accepted'
        );
        if (acceptedBid) {
          await Project.findByIdAndUpdate(project._id, {
            winningBid: acceptedBid._id,
            assignedVendor: acceptedBid.vendor
          });
        }
      }
    }

    return createdBids;
  } catch (error) {
    console.error('❌ Error seeding bids:', error);
    return [];
  }
};

export { connectDB, clearData, seedUsers, seedProjects, seedBids, demoUsers, demoProjects };
