# GlobalConnect Comprehensive Test Suite

A comprehensive CLI test suite that validates all features listed in the GlobalConnect demo guide.

## 🎯 Features Tested

### 📋 Project Management
- ✅ Browse 8+ demo projects across different categories
- ✅ Create new projects with budget and timeline
- ✅ Manage project status and milestones
- ✅ View project analytics and performance

### 💰 Bidding System
- ✅ View 20+ realistic bids on various projects
- ✅ Submit detailed proposals with attachments
- ✅ Accept or reject bids as a client
- ✅ Track bid status and negotiations

### 💬 Real-time Messaging
- ✅ Send messages with file attachments
- ✅ Create project-specific conversations
- ✅ Real-time message delivery and read receipts
- ✅ Group conversations for team collaboration

### 📁 Document Management
- ✅ Upload documents with security levels
- ✅ Version control and change tracking
- ✅ Role-based access permissions
- ✅ 30+ demo documents across file types

### 📊 Analytics Dashboard
- ✅ View personalized dashboard statistics
- ✅ Project performance analytics
- ✅ Revenue and earnings tracking
- ✅ User activity and engagement metrics

### 👑 Admin Panel
- ✅ User management and role assignment
- ✅ Platform-wide analytics and reporting
- ✅ System configuration and settings
- ✅ Content moderation and security

## 🚀 Quick Start

### Prerequisites
- Node.js 14+ installed
- GlobalConnect backend running on localhost:5001
- Demo data populated in the database

### Installation
```bash
cd test-suite
npm install
```

### Run All Tests
```bash
npm test
```

### Run Individual Test Suites
```bash
# Project Management Tests
npm run test:projects

# Bidding System Tests
npm run test:bidding

# Messaging System Tests
npm run test:messaging

# Document Management Tests
npm run test:documents

# Analytics Dashboard Tests
npm run test:analytics

# Admin Panel Tests
npm run test:admin
```

## 📊 Test Reports

The test suite generates three types of reports:

1. **JSON Report** (`comprehensive-test-report.json`) - Detailed machine-readable results
2. **Markdown Report** (`comprehensive-test-report.md`) - Human-readable summary
3. **HTML Report** (`comprehensive-test-report.html`) - Interactive web report

## 🔧 Configuration

### Demo Credentials
The test suite uses the following demo accounts from `DEMO_DATA_INFO.md`:

**Clients:**
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123

**Vendors:**
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123

**Admin:**
- <EMAIL> / admin123

### API Configuration
Tests are configured to run against:
- **Base URL**: `http://localhost:5001/api`
- **Backend Port**: 5001

To change the configuration, modify the `BASE_URL` constant in `comprehensive-feature-test.js`.

## 📋 Test Structure

```
test-suite/
├── comprehensive-feature-test.js    # Base test runner and utilities
├── test-project-management.js       # Project management tests
├── test-bidding-system.js          # Bidding system tests
├── test-messaging-system.js        # Messaging system tests
├── test-document-management.js     # Document management tests
├── test-analytics-dashboard.js     # Analytics dashboard tests
├── test-admin-panel.js             # Admin panel tests
├── run-all-tests.js                # Main test orchestrator
├── package.json                    # Dependencies and scripts
└── README.md                       # This file
```

## 🧪 Test Categories

### Authentication Tests
- ✅ User login with demo credentials
- ✅ Role-based access control
- ✅ Token-based authentication

### API Endpoint Tests
- ✅ GET requests for data retrieval
- ✅ POST requests for data creation
- ✅ PUT requests for data updates
- ✅ DELETE requests for data removal

### Data Validation Tests
- ✅ Response structure validation
- ✅ Required field verification
- ✅ Data type checking
- ✅ Business logic validation

### Permission Tests
- ✅ Role-based access restrictions
- ✅ Resource ownership validation
- ✅ Admin-only functionality

## 📈 Success Criteria

- **Overall Success Rate**: Target 80%+
- **Critical Features**: 100% (Authentication, Core CRUD operations)
- **Advanced Features**: 70%+ (Analytics, Real-time features)
- **Admin Features**: 60%+ (May have some unimplemented endpoints)

## 🐛 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:5001
   ```
   - Ensure the backend server is running on port 5001
   - Check if the backend is accessible at `http://localhost:5001`

2. **Authentication Failures**
   ```
   Error: Authentication failed for client
   ```
   - Verify demo data is populated in the database
   - Check that user credentials match `DEMO_DATA_INFO.md`

3. **Missing Endpoints**
   ```
   Error: Request failed: GET /api/some-endpoint - 404
   ```
   - This is expected for some advanced features
   - Check the test report for implementation status

### Debug Mode
To enable verbose logging, set the environment variable:
```bash
DEBUG=true npm test
```

## 📝 Contributing

To add new tests:

1. Create a new test file following the naming pattern `test-[feature-name].js`
2. Extend the base `TestRunner` class
3. Add the test suite to `run-all-tests.js`
4. Update this README with the new feature coverage

## 📄 License

MIT License - See LICENSE file for details.

---

**Generated by GlobalConnect Test Suite v1.0.0**
