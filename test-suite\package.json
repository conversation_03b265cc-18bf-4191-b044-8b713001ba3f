{"name": "globalconnect-test-suite", "version": "1.0.0", "description": "Comprehensive test suite for GlobalConnect platform features", "main": "run-all-tests.js", "scripts": {"test": "node run-all-tests.js", "test:projects": "node test-project-management.js", "test:bidding": "node test-bidding-system.js", "test:messaging": "node test-messaging-system.js", "test:documents": "node test-document-management.js", "test:analytics": "node test-analytics-dashboard.js", "test:admin": "node test-admin-panel.js", "install-deps": "npm install", "setup": "npm install && echo 'Test suite setup complete!'"}, "dependencies": {"axios": "^1.6.0"}, "devDependencies": {}, "keywords": ["testing", "api", "globalconnect", "automation", "cli"], "author": "GlobalConnect Team", "license": "MIT", "engines": {"node": ">=14.0.0"}}