import express from 'express';
import {
  getUserAnalytics,
  getProjectAnalytics,
  getPlatformAnalytics,
  trackEvent,
  getDashboardStats,
  getRevenueAnalytics,
  getUserActivityAnalytics,
  getProjectPerformanceAnalytics
} from '../controllers/analyticsController.js';
import { authMiddleware, adminMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Track analytics event
router.post('/track', trackEvent);

// Get dashboard statistics for current user
router.get('/dashboard', getDashboardStats);

// Get user analytics (with optional userId parameter)
router.get('/user', getUserAnalytics);

// Get user analytics by specific user ID
router.get('/user/:userId', getUserAnalytics);

// Get project analytics
router.get('/project/:projectId', getProjectAnalytics);

// Get user activity analytics
router.get('/user/:userId/activity', getUserActivityAnalytics);

// Get project performance analytics
router.get('/project/:projectId/performance', getProjectPerformanceAnalytics);

// Admin only routes
router.get('/platform', adminMiddleware, getPlatformAnalytics);
router.get('/revenue', adminMiddleware, getRevenueAnalytics);

export default router;
