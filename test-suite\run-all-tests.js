#!/usr/bin/env node

/**
 * Main Test Runner - Executes all feature tests
 * Generates comprehensive test report for all GlobalConnect features
 */

const fs = require('fs');
const path = require('path');
const { TestRunner } = require('./comprehensive-feature-test');

// Import all test suites
const ProjectManagementTests = require('./test-project-management');
const BiddingSystemTests = require('./test-bidding-system');
const MessagingSystemTests = require('./test-messaging-system');
const DocumentManagementTests = require('./test-document-management');
const AnalyticsDashboardTests = require('./test-analytics-dashboard');
const AdminPanelTests = require('./test-admin-panel');

class ComprehensiveTestSuite {
  constructor() {
    this.allResults = [];
    this.startTime = new Date();
  }

  async runAllTests() {
    console.log('🚀 GlobalConnect Comprehensive Feature Test Suite');
    console.log('=' .repeat(60));
    console.log(`Started at: ${this.startTime.toISOString()}`);
    console.log('=' .repeat(60));

    const testSuites = [
      { name: 'Project Management', class: ProjectManagementTests, icon: '📋' },
      { name: 'Bidding System', class: BiddingSystemTests, icon: '💰' },
      { name: 'Messaging System', class: MessagingSystemTests, icon: '💬' },
      { name: 'Document Management', class: DocumentManagementTests, icon: '📁' },
      { name: 'Analytics Dashboard', class: AnalyticsDashboardTests, icon: '📊' },
      { name: 'Admin Panel', class: AdminPanelTests, icon: '👑' }
    ];

    for (const suite of testSuites) {
      console.log(`\n${suite.icon} Running ${suite.name} Tests...`);
      console.log('-'.repeat(40));

      try {
        const testInstance = new suite.class();
        const report = await testInstance.runAllTests();
        
        this.allResults.push({
          suiteName: suite.name,
          icon: suite.icon,
          report: report,
          success: true
        });

        console.log(`✅ ${suite.name} Tests Completed`);
        console.log(`   Passed: ${report.summary.passed}/${report.summary.total}`);
        console.log(`   Success Rate: ${report.summary.successRate}%`);

      } catch (error) {
        console.error(`❌ ${suite.name} Tests Failed: ${error.message}`);
        
        this.allResults.push({
          suiteName: suite.name,
          icon: suite.icon,
          report: null,
          success: false,
          error: error.message
        });
      }
    }

    return this.generateComprehensiveReport();
  }

  generateComprehensiveReport() {
    const endTime = new Date();
    const totalDuration = endTime - this.startTime;

    // Calculate overall statistics
    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    const suiteResults = [];

    this.allResults.forEach(result => {
      if (result.success && result.report) {
        totalTests += result.report.summary.total;
        totalPassed += result.report.summary.passed;
        totalFailed += result.report.summary.failed;
        
        suiteResults.push({
          name: result.suiteName,
          icon: result.icon,
          total: result.report.summary.total,
          passed: result.report.summary.passed,
          failed: result.report.summary.failed,
          successRate: result.report.summary.successRate,
          tests: result.report.tests
        });
      } else {
        suiteResults.push({
          name: result.suiteName,
          icon: result.icon,
          total: 0,
          passed: 0,
          failed: 1,
          successRate: 0,
          error: result.error,
          tests: []
        });
        totalFailed += 1;
      }
    });

    const overallSuccessRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(2) : 0;

    // Generate comprehensive report
    const comprehensiveReport = {
      summary: {
        startTime: this.startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration: totalDuration,
        totalSuites: this.allResults.length,
        totalTests: totalTests,
        totalPassed: totalPassed,
        totalFailed: totalFailed,
        overallSuccessRate: overallSuccessRate
      },
      suites: suiteResults,
      generatedAt: endTime.toISOString()
    };

    // Save detailed JSON report
    fs.writeFileSync(
      path.join(__dirname, 'comprehensive-test-report.json'),
      JSON.stringify(comprehensiveReport, null, 2)
    );

    // Generate markdown report
    this.generateMarkdownReport(comprehensiveReport);

    // Generate HTML report
    this.generateHtmlReport(comprehensiveReport);

    // Print summary to console
    this.printSummary(comprehensiveReport);

    return comprehensiveReport;
  }

  generateMarkdownReport(report) {
    const markdown = `# GlobalConnect Comprehensive Feature Test Report

## Executive Summary

- **Test Duration**: ${Math.round(report.summary.duration / 1000)} seconds
- **Total Test Suites**: ${report.summary.totalSuites}
- **Total Tests**: ${report.summary.totalTests}
- **Passed**: ${report.summary.totalPassed} ✅
- **Failed**: ${report.summary.totalFailed} ❌
- **Overall Success Rate**: ${report.summary.overallSuccessRate}%

## Test Suite Results

${report.suites.map(suite => `
### ${suite.icon} ${suite.name}

- **Tests**: ${suite.total}
- **Passed**: ${suite.passed}
- **Failed**: ${suite.failed}
- **Success Rate**: ${suite.successRate}%
${suite.error ? `- **Error**: ${suite.error}` : ''}

${suite.tests && suite.tests.length > 0 ? `
#### Individual Test Results

${suite.tests.map(test => `
##### ${test.passed ? '✅' : '❌'} ${test.name}
- **Description**: ${test.description}
- **Duration**: ${test.duration}ms
${test.errors.length > 0 ? `- **Errors**: ${test.errors.join(', ')}` : ''}
`).join('')}
` : ''}
`).join('')}

## Feature Coverage Analysis

### ✅ Tested Features

${this.getTestedFeatures(report).map(feature => `- ${feature}`).join('\n')}

### ⚠️ Areas Needing Attention

${this.getFailedFeatures(report).map(feature => `- ${feature}`).join('\n')}

## Recommendations

${this.generateRecommendations(report).map(rec => `- ${rec}`).join('\n')}

---

*Report generated on ${report.generatedAt}*
`;

    fs.writeFileSync(
      path.join(__dirname, 'comprehensive-test-report.md'),
      markdown
    );
  }

  generateHtmlReport(report) {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GlobalConnect Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .metric-label { color: #7f8c8d; margin-top: 5px; }
        .suite { margin-bottom: 30px; border: 1px solid #e1e8ed; border-radius: 6px; overflow: hidden; }
        .suite-header { background: #3498db; color: white; padding: 15px; font-weight: bold; }
        .suite-content { padding: 15px; }
        .test-item { padding: 10px; border-bottom: 1px solid #ecf0f1; }
        .test-item:last-child { border-bottom: none; }
        .passed { color: #27ae60; }
        .failed { color: #e74c3c; }
        .success-rate { font-weight: bold; }
        .success-high { color: #27ae60; }
        .success-medium { color: #f39c12; }
        .success-low { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GlobalConnect Comprehensive Test Report</h1>
            <p>Generated on ${report.generatedAt}</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">${report.summary.totalTests}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">${report.summary.totalPassed}</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">${report.summary.totalFailed}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value success-rate ${this.getSuccessRateClass(report.summary.overallSuccessRate)}">${report.summary.overallSuccessRate}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>

        ${report.suites.map(suite => `
        <div class="suite">
            <div class="suite-header">
                ${suite.icon} ${suite.name} - ${suite.passed}/${suite.total} tests passed (${suite.successRate}%)
            </div>
            <div class="suite-content">
                ${suite.error ? `<p class="failed">Error: ${suite.error}</p>` : ''}
                ${suite.tests && suite.tests.length > 0 ? suite.tests.map(test => `
                <div class="test-item">
                    <span class="${test.passed ? 'passed' : 'failed'}">${test.passed ? '✅' : '❌'}</span>
                    <strong>${test.name}</strong> - ${test.description}
                    <br><small>Duration: ${test.duration}ms</small>
                    ${test.errors.length > 0 ? `<br><small class="failed">Errors: ${test.errors.join(', ')}</small>` : ''}
                </div>
                `).join('') : '<p>No detailed test results available</p>'}
            </div>
        </div>
        `).join('')}
    </div>
</body>
</html>`;

    fs.writeFileSync(
      path.join(__dirname, 'comprehensive-test-report.html'),
      html
    );
  }

  getSuccessRateClass(rate) {
    if (rate >= 80) return 'success-high';
    if (rate >= 60) return 'success-medium';
    return 'success-low';
  }

  getTestedFeatures(report) {
    const features = [];
    report.suites.forEach(suite => {
      if (suite.tests && suite.tests.length > 0) {
        suite.tests.forEach(test => {
          if (test.passed) {
            features.push(`${suite.icon} ${test.name} (${suite.name})`);
          }
        });
      }
    });
    return features;
  }

  getFailedFeatures(report) {
    const features = [];
    report.suites.forEach(suite => {
      if (suite.error) {
        features.push(`${suite.icon} ${suite.name} - Suite failed to run`);
      } else if (suite.tests && suite.tests.length > 0) {
        suite.tests.forEach(test => {
          if (!test.passed) {
            features.push(`${suite.icon} ${test.name} (${suite.name})`);
          }
        });
      }
    });
    return features;
  }

  generateRecommendations(report) {
    const recommendations = [];
    
    if (report.summary.overallSuccessRate < 80) {
      recommendations.push('Overall success rate is below 80%. Focus on fixing failing tests.');
    }
    
    if (report.summary.totalFailed > 0) {
      recommendations.push('Some tests are failing. Review error messages and implement missing functionality.');
    }
    
    const failedSuites = report.suites.filter(s => s.successRate < 50);
    if (failedSuites.length > 0) {
      recommendations.push(`Priority areas: ${failedSuites.map(s => s.name).join(', ')} need immediate attention.`);
    }
    
    recommendations.push('Implement missing API endpoints identified in test failures.');
    recommendations.push('Add comprehensive demo data to support all test scenarios.');
    recommendations.push('Consider adding integration tests for end-to-end workflows.');
    
    return recommendations;
  }

  printSummary(report) {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 COMPREHENSIVE TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Duration: ${Math.round(report.summary.duration / 1000)} seconds`);
    console.log(`Total Suites: ${report.summary.totalSuites}`);
    console.log(`Total Tests: ${report.summary.totalTests}`);
    console.log(`Passed: ${report.summary.totalPassed} ✅`);
    console.log(`Failed: ${report.summary.totalFailed} ❌`);
    console.log(`Overall Success Rate: ${report.summary.overallSuccessRate}%`);
    console.log('='.repeat(60));
    
    console.log('\n📊 Suite Breakdown:');
    report.suites.forEach(suite => {
      const status = suite.successRate >= 80 ? '✅' : suite.successRate >= 50 ? '⚠️' : '❌';
      console.log(`${status} ${suite.icon} ${suite.name}: ${suite.passed}/${suite.total} (${suite.successRate}%)`);
    });
    
    console.log('\n📁 Reports Generated:');
    console.log('- comprehensive-test-report.json (Detailed JSON)');
    console.log('- comprehensive-test-report.md (Markdown)');
    console.log('- comprehensive-test-report.html (HTML)');
    console.log('='.repeat(60));
  }
}

// Run comprehensive tests if called directly
if (require.main === module) {
  const testSuite = new ComprehensiveTestSuite();
  
  testSuite.runAllTests().then(report => {
    const exitCode = report.summary.totalFailed > 0 ? 1 : 0;
    console.log(`\n🏁 Test suite completed with exit code: ${exitCode}`);
    process.exit(exitCode);
  }).catch(error => {
    console.error('❌ Test suite execution failed:', error);
    process.exit(1);
  });
}

module.exports = ComprehensiveTestSuite;
