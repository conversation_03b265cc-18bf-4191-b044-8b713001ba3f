import React, { useState } from 'react';
import { Video, Calendar, Clock, Users, Play, BookOpen } from 'lucide-react';

export default function Webinars() {
  const [activeTab, setActiveTab] = useState('upcoming');

  const upcomingWebinars = [
    {
      title: "Mastering Remote Project Management",
      date: "2024-08-15",
      time: "2:00 PM EST",
      duration: "60 minutes",
      speaker: "<PERSON>",
      speakerTitle: "Senior Project Manager",
      attendees: 245,
      description: "Learn best practices for managing remote teams and projects effectively.",
      image: "🎯"
    },
    {
      title: "Freelancer's Guide to Pricing Your Services",
      date: "2024-08-22",
      time: "1:00 PM EST",
      duration: "45 minutes",
      speaker: "<PERSON>",
      speakerTitle: "Business Consultant",
      attendees: 189,
      description: "Discover strategies to price your services competitively and profitably.",
      image: "💰"
    },
    {
      title: "Building Long-term Client Relationships",
      date: "2024-08-29",
      time: "3:00 PM EST",
      duration: "50 minutes",
      speaker: "<PERSON>",
      speakerTitle: "Client Success Manager",
      attendees: 156,
      description: "Learn how to turn one-time clients into long-term business partners.",
      image: "🤝"
    }
  ];

  const pastWebinars = [
    {
      title: "Digital Marketing Trends 2024",
      date: "2024-07-18",
      views: 1250,
      speaker: "<PERSON> <PERSON>",
      duration: "55 minutes",
      image: "📈"
    },
    {
      title: "Web Development Best Practices",
      date: "2024-07-11",
      views: 980,
      speaker: "Lisa Wang",
      duration: "65 minutes",
      image: "💻"
    },
    {
      title: "Effective Communication in Remote Work",
      date: "2024-07-04",
      views: 1100,
      speaker: "James Wilson",
      duration: "40 minutes",
      image: "💬"
    }
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-indigo-600 to-purple-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Video className="w-16 h-16 mx-auto mb-6 text-indigo-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Webinars & Training
            </h1>
            <p className="text-xl text-indigo-100 leading-relaxed">
              Join our expert-led webinars to enhance your skills, learn industry best practices, and stay ahead of the curve.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-secondary-200 sticky top-0 z-10">
        <div className="container-custom">
          <div className="flex space-x-8">
            {[
              { id: 'upcoming', label: 'Upcoming Webinars' },
              { id: 'past', label: 'Past Webinars' },
              { id: 'series', label: 'Training Series' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            {/* Upcoming Webinars */}
            {activeTab === 'upcoming' && (
              <div className="space-y-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                    Upcoming Webinars
                  </h2>
                  <p className="text-lg text-secondary-600">
                    Register now for our upcoming sessions
                  </p>
                </div>

                <div className="space-y-6">
                  {upcomingWebinars.map((webinar, index) => (
                    <div key={index} className="card p-6 hover:shadow-lg transition-shadow duration-300">
                      <div className="flex flex-col lg:flex-row items-start space-y-4 lg:space-y-0 lg:space-x-6">
                        <div className="flex-shrink-0">
                          <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-200 rounded-lg flex items-center justify-center text-2xl">
                            {webinar.image}
                          </div>
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4">
                            <div>
                              <h3 className="text-xl font-bold text-secondary-900 mb-2">
                                {webinar.title}
                              </h3>
                              <p className="text-secondary-600 mb-3">
                                {webinar.description}
                              </p>
                              <div className="flex items-center space-x-4 text-sm text-secondary-500">
                                <div className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-1" />
                                  {new Date(webinar.date).toLocaleDateString()}
                                </div>
                                <div className="flex items-center">
                                  <Clock className="w-4 h-4 mr-1" />
                                  {webinar.time}
                                </div>
                                <div className="flex items-center">
                                  <Users className="w-4 h-4 mr-1" />
                                  {webinar.attendees} registered
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex-shrink-0 mt-4 lg:mt-0">
                              <button className="btn-primary">
                                Register Now
                              </button>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-3 pt-4 border-t border-secondary-200">
                            <div className="w-8 h-8 bg-secondary-200 rounded-full flex items-center justify-center">
                              👤
                            </div>
                            <div>
                              <div className="font-medium text-secondary-900">{webinar.speaker}</div>
                              <div className="text-sm text-secondary-600">{webinar.speakerTitle}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Past Webinars */}
            {activeTab === 'past' && (
              <div className="space-y-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                    Past Webinars
                  </h2>
                  <p className="text-lg text-secondary-600">
                    Watch recordings of our previous sessions
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {pastWebinars.map((webinar, index) => (
                    <div key={index} className="card p-6 hover:shadow-lg transition-shadow duration-300">
                      <div className="text-center mb-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg flex items-center justify-center text-2xl mx-auto mb-4">
                          {webinar.image}
                        </div>
                        <h3 className="text-lg font-bold text-secondary-900 mb-2">
                          {webinar.title}
                        </h3>
                        <p className="text-sm text-secondary-600 mb-3">
                          by {webinar.speaker}
                        </p>
                        <div className="flex items-center justify-center space-x-4 text-xs text-secondary-500 mb-4">
                          <span>{new Date(webinar.date).toLocaleDateString()}</span>
                          <span>{webinar.duration}</span>
                          <span>{webinar.views} views</span>
                        </div>
                      </div>
                      
                      <button className="w-full btn-secondary flex items-center justify-center">
                        <Play className="w-4 h-4 mr-2" />
                        Watch Recording
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Training Series */}
            {activeTab === 'series' && (
              <div className="space-y-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                    Training Series
                  </h2>
                  <p className="text-lg text-secondary-600">
                    Comprehensive training programs for skill development
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  <div className="card p-6">
                    <BookOpen className="w-8 h-8 text-blue-600 mb-4" />
                    <h3 className="text-xl font-bold text-secondary-900 mb-3">
                      Freelancer Mastery Series
                    </h3>
                    <p className="text-secondary-600 mb-4">
                      A 6-part series covering everything from finding clients to scaling your freelance business.
                    </p>
                    <div className="space-y-2 text-sm text-secondary-600 mb-6">
                      <div>• Building Your Portfolio</div>
                      <div>• Client Acquisition Strategies</div>
                      <div>• Pricing and Negotiation</div>
                      <div>• Project Management</div>
                      <div>• Scaling Your Business</div>
                      <div>• Legal and Financial Basics</div>
                    </div>
                    <button className="btn-primary w-full">
                      Enroll Now - $99
                    </button>
                  </div>

                  <div className="card p-6">
                    <BookOpen className="w-8 h-8 text-green-600 mb-4" />
                    <h3 className="text-xl font-bold text-secondary-900 mb-3">
                      Client Success Program
                    </h3>
                    <p className="text-secondary-600 mb-4">
                      A 4-part series designed to help businesses maximize their success on the platform.
                    </p>
                    <div className="space-y-2 text-sm text-secondary-600 mb-6">
                      <div>• Writing Effective Project Briefs</div>
                      <div>• Evaluating and Selecting Freelancers</div>
                      <div>• Managing Remote Teams</div>
                      <div>• Quality Assurance and Feedback</div>
                    </div>
                    <button className="btn-primary w-full">
                      Enroll Now - $79
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Newsletter Signup */}
      <div className="py-16 bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Never Miss a Webinar
            </h2>
            <p className="text-indigo-100 mb-8">
              Subscribe to our newsletter to get notified about upcoming webinars and training opportunities.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg text-secondary-900 placeholder-secondary-500"
              />
              <button className="bg-white text-indigo-600 hover:bg-indigo-50 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
