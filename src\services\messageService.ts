import axios from 'axios';
import { io, Socket } from 'socket.io-client';

const API_URL = 'http://localhost:5001/api/messages';
const SOCKET_URL = 'http://localhost:5001';

export interface User {
  _id: string;
  name: string;
  email: string;
  profileImage?: string;
  role: string;
  company?: string;
}

export interface Message {
  _id: string;
  conversation: string;
  sender: User;
  content: string;
  attachments: Array<{
    fileKey: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    url: string;
  }>;
  readBy: Array<{
    user: string;
    readAt: string;
  }>;
  deletedFor: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  _id: string;
  participants: User[];
  project?: {
    _id: string;
    title: string;
    status: string;
  };
  title: string;
  type: 'direct' | 'group' | 'project';
  lastMessage?: Message;
  unreadCount: Map<string, number> | Record<string, number>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateConversationData {
  participantIds: string[];
  projectId?: string;
  title?: string;
  type?: 'direct' | 'group' | 'project';
}

export interface SendMessageData {
  content?: string;
  attachments?: File[];
}

class MessageService {
  private socket: Socket | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  // Initialize socket connection
  initializeSocket(userId: string): void {
    if (this.socket) {
      this.socket.disconnect();
    }

    this.socket = io(SOCKET_URL, {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    this.socket.on('connect', () => {
      console.log('Connected to messaging server');
      this.socket?.emit('join_user_room', userId);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from messaging server');
    });

    // Set up event listeners
    this.socket.on('new_message', (data) => {
      this.emit('new_message', data);
    });

    this.socket.on('new_conversation', (data) => {
      this.emit('new_conversation', data);
    });

    this.socket.on('message_read', (data) => {
      this.emit('message_read', data);
    });

    this.socket.on('message_deleted', (data) => {
      this.emit('message_deleted', data);
    });

    this.socket.on('user_typing', (data) => {
      this.emit('user_typing', data);
    });

    this.socket.on('user_stopped_typing', (data) => {
      this.emit('user_stopped_typing', data);
    });
  }

  // Disconnect socket
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  // Event listener management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // API methods
  async getConversations(): Promise<{ conversations: Conversation[]; totalUnreadCount: number }> {
    try {
      const response = await axios.get(`${API_URL}/conversations`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch conversations');
    }
  }

  async getConversationById(conversationId: string): Promise<Conversation> {
    try {
      const response = await axios.get(`${API_URL}/conversations/${conversationId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch conversation');
    }
  }

  async createConversation(data: CreateConversationData): Promise<Conversation> {
    try {
      const response = await axios.post(`${API_URL}/conversations`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create conversation');
    }
  }

  async getMessages(conversationId: string, page: number = 1, limit: number = 20): Promise<{
    messages: Message[];
    totalMessages: number;
    currentPage: number;
    totalPages: number;
  }> {
    try {
      const response = await axios.get(`${API_URL}/conversations/${conversationId}/messages`, {
        params: { page, limit }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch messages');
    }
  }

  async sendMessage(conversationId: string, data: SendMessageData): Promise<Message> {
    try {
      const formData = new FormData();
      
      if (data.content) {
        formData.append('content', data.content);
      }

      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append('attachments', file);
        });
      }

      const response = await axios.post(
        `${API_URL}/conversations/${conversationId}/messages`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send message');
    }
  }

  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      await axios.put(`${API_URL}/messages/${messageId}/read`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to mark message as read');
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/messages/${messageId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete message');
    }
  }

  // Socket methods
  joinConversation(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('join_conversation', conversationId);
    }
  }

  leaveConversation(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('leave_conversation', conversationId);
    }
  }

  startTyping(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('typing_start', { conversationId });
    }
  }

  stopTyping(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('typing_stop', { conversationId });
    }
  }

  // Utility methods
  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(fileType: string): string {
    if (fileType.startsWith('image/')) return '🖼️';
    if (fileType.startsWith('video/')) return '🎥';
    if (fileType.startsWith('audio/')) return '🎵';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📈';
    if (fileType.includes('zip') || fileType.includes('rar')) return '🗜️';
    return '📎';
  }

  isMessageFromCurrentUser(message: Message, currentUserId: string): boolean {
    return message.sender._id === currentUserId;
  }

  isMessageRead(message: Message, userId: string): boolean {
    return message.readBy.some(read => read.user === userId);
  }

  getUnreadCount(conversation: Conversation, userId: string): number {
    // Handle both Map and plain object cases
    if (conversation.unreadCount instanceof Map) {
      return conversation.unreadCount.get(userId) || 0;
    } else if (conversation.unreadCount && typeof conversation.unreadCount === 'object') {
      // Handle plain object case (from API response)
      return (conversation.unreadCount as any)[userId] || 0;
    }
    return 0;
  }

  getConversationTitle(conversation: Conversation, currentUserId: string): string {
    if (conversation.title) {
      return conversation.title;
    }

    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(p => p._id !== currentUserId);
      return otherParticipant?.name || 'Unknown User';
    }

    if (conversation.project) {
      return `Project: ${conversation.project.title}`;
    }

    return `Group (${conversation.participants.length} members)`;
  }

  getConversationAvatar(conversation: Conversation, currentUserId: string): string | null {
    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(p => p._id !== currentUserId);
      return otherParticipant?.profileImage || null;
    }
    return null;
  }
}

export const messageService = new MessageService();
