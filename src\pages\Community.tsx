import React from 'react';
import { Users, MessageCircle, Heart, Award, Calendar, ExternalLink } from 'lucide-react';

export default function Community() {
  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-600 to-blue-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Users className="w-16 h-16 mx-auto mb-6 text-green-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Join Our Community
            </h1>
            <p className="text-xl text-green-100 leading-relaxed">
              Connect with thousands of freelancers, clients, and industry experts. Share knowledge, find opportunities, and grow together.
            </p>
          </div>
        </div>
      </div>

      {/* Community Stats */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div className="card p-6">
                <div className="text-3xl font-bold text-primary-600 mb-2">50K+</div>
                <div className="text-secondary-600">Active Members</div>
              </div>
              <div className="card p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">1.2M+</div>
                <div className="text-secondary-600">Forum Posts</div>
              </div>
              <div className="card p-6">
                <div className="text-3xl font-bold text-green-600 mb-2">500+</div>
                <div className="text-secondary-600">Events Hosted</div>
              </div>
              <div className="card p-6">
                <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
                <div className="text-secondary-600">Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Community Features */}
      <div className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                What Our Community Offers
              </h2>
              <p className="text-lg text-secondary-600">
                Discover the benefits of being part of the GlobalConnect community
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="card p-6">
                <MessageCircle className="w-8 h-8 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Discussion Forums
                </h3>
                <p className="text-secondary-600">
                  Engage in meaningful discussions about industry trends, best practices, and project challenges.
                </p>
              </div>

              <div className="card p-6">
                <Calendar className="w-8 h-8 text-green-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Regular Events
                </h3>
                <p className="text-secondary-600">
                  Join webinars, workshops, and networking events to expand your skills and connections.
                </p>
              </div>

              <div className="card p-6">
                <Award className="w-8 h-8 text-purple-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Recognition Program
                </h3>
                <p className="text-secondary-600">
                  Get recognized for your contributions and achievements within the community.
                </p>
              </div>

              <div className="card p-6">
                <Heart className="w-8 h-8 text-red-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Mentorship
                </h3>
                <p className="text-secondary-600">
                  Connect with experienced professionals who can guide your career growth.
                </p>
              </div>

              <div className="card p-6">
                <Users className="w-8 h-8 text-indigo-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Local Chapters
                </h3>
                <p className="text-secondary-600">
                  Join local community chapters for in-person networking and collaboration.
                </p>
              </div>

              <div className="card p-6">
                <ExternalLink className="w-8 h-8 text-orange-600 mb-4" />
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Resource Library
                </h3>
                <p className="text-secondary-600">
                  Access exclusive resources, templates, and tools shared by community members.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Community Guidelines */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              Community Guidelines
            </h2>
            
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Be Respectful and Professional
                </h3>
                <p className="text-secondary-600">
                  Treat all community members with respect and maintain professional communication at all times.
                </p>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Share Knowledge Generously
                </h3>
                <p className="text-secondary-600">
                  Help others by sharing your expertise, experiences, and insights. Knowledge sharing makes our community stronger.
                </p>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  No Spam or Self-Promotion
                </h3>
                <p className="text-secondary-600">
                  Keep discussions relevant and valuable. Excessive self-promotion or spam will not be tolerated.
                </p>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Protect Privacy and Confidentiality
                </h3>
                <p className="text-secondary-600">
                  Respect the privacy of others and don't share confidential information about projects or clients.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Join CTA */}
      <div className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Join Our Community?
            </h2>
            <p className="text-primary-100 mb-8">
              Connect with like-minded professionals and take your career to the next level.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                Join Community Forum
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                View Upcoming Events
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
