import Document from '../models/documentModel.js';

// Sample documents for different project types
const documentTemplates = [
  {
    title: 'Project Requirements Document',
    description: 'Detailed requirements and specifications for the project',
    fileName: 'requirements.pdf',
    fileType: 'application/pdf',
    fileSize: 245760, // ~240KB
    securityLevel: 2
  },
  {
    title: 'Wireframes and Mockups',
    description: 'Initial wireframes and design mockups',
    fileName: 'wireframes.fig',
    fileType: 'application/octet-stream',
    fileSize: 1048576, // 1MB
    securityLevel: 1
  },
  {
    title: 'Technical Specification',
    description: 'Technical architecture and implementation details',
    fileName: 'tech-spec.docx',
    fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    fileSize: 512000, // 500KB
    securityLevel: 2
  },
  {
    title: 'Brand Guidelines',
    description: 'Brand identity guidelines and assets',
    fileName: 'brand-guidelines.pdf',
    fileType: 'application/pdf',
    fileSize: 2097152, // 2MB
    securityLevel: 1
  },
  {
    title: 'Database Schema',
    description: 'Database design and entity relationship diagram',
    fileName: 'database-schema.sql',
    fileType: 'application/sql',
    fileSize: 51200, // 50KB
    securityLevel: 3
  },
  {
    title: 'API Documentation',
    description: 'REST API endpoints and documentation',
    fileName: 'api-docs.json',
    fileType: 'application/json',
    fileSize: 102400, // 100KB
    securityLevel: 2
  },
  {
    title: 'User Research Report',
    description: 'User interviews and research findings',
    fileName: 'user-research.pdf',
    fileType: 'application/pdf',
    fileSize: 1572864, // 1.5MB
    securityLevel: 1
  },
  {
    title: 'Project Timeline',
    description: 'Detailed project timeline and milestones',
    fileName: 'timeline.xlsx',
    fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    fileSize: 204800, // 200KB
    securityLevel: 1
  },
  {
    title: 'Style Guide',
    description: 'UI style guide and component library',
    fileName: 'style-guide.sketch',
    fileType: 'application/octet-stream',
    fileSize: 3145728, // 3MB
    securityLevel: 1
  },
  {
    title: 'Test Cases',
    description: 'Comprehensive test cases and scenarios',
    fileName: 'test-cases.xlsx',
    fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    fileSize: 307200, // 300KB
    securityLevel: 2
  }
];

// Generate a unique file key
const generateFileKey = () => {
  return `documents/${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
};

// Seed documents
export const seedDocuments = async (projects, users) => {
  try {
    const documents = [];
    
    // Create documents for each project
    for (const project of projects) {
      const numDocs = Math.floor(Math.random() * 4) + 2; // 2-5 documents per project
      const projectUsers = users.filter(user => 
        user._id.toString() === project.client.toString() || 
        user.role === 'vendor'
      );
      
      for (let i = 0; i < numDocs; i++) {
        const template = documentTemplates[i % documentTemplates.length];
        const uploader = projectUsers[Math.floor(Math.random() * projectUsers.length)];
        
        const document = {
          title: template.title,
          description: template.description,
          project: project._id,
          uploadedBy: uploader._id,
          updatedBy: uploader._id,
          fileKey: generateFileKey(),
          fileName: template.fileName,
          fileType: template.fileType,
          fileSize: template.fileSize,
          securityLevel: template.securityLevel,
          permissions: [project.client.toString()], // Client always has access
          metadata: {
            projectTitle: project.title,
            category: project.category,
            uploadedAt: new Date(),
            tags: project.skills.slice(0, 3)
          },
          versions: [
            {
              version: 1,
              fileKey: generateFileKey(),
              updatedBy: uploader._id,
              updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date within last week
              description: 'Initial version'
            }
          ],
          isDeleted: false
        };
        
        // Add vendor permissions for some documents
        if (project.assignedVendor && Math.random() > 0.3) {
          document.permissions.push(project.assignedVendor.toString());
        }
        
        // Set creation date (within last month)
        const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
        document.createdAt = createdAt;
        document.updatedAt = createdAt;
        
        documents.push(document);
      }
    }
    
    // Create some additional versions for random documents
    const createdDocuments = await Document.insertMany(documents);
    
    // Add additional versions to some documents
    for (let i = 0; i < Math.min(createdDocuments.length / 3, 10); i++) {
      const doc = createdDocuments[i];
      const additionalVersions = Math.floor(Math.random() * 3) + 1; // 1-3 additional versions
      
      for (let v = 2; v <= additionalVersions + 1; v++) {
        const versionDate = new Date(doc.createdAt.getTime() + v * 24 * 60 * 60 * 1000);
        doc.versions.push({
          version: v,
          fileKey: generateFileKey(),
          updatedBy: doc.uploadedBy,
          updatedAt: versionDate,
          description: `Version ${v} - Updated based on feedback`
        });
        doc.updatedAt = versionDate;
      }
      
      await doc.save();
    }
    
    console.log(`✅ Created ${createdDocuments.length} documents with versions`);
    return createdDocuments;
  } catch (error) {
    console.error('❌ Error seeding documents:', error);
    return [];
  }
};
