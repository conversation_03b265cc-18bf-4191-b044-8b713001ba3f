import React from 'react';
import { Link } from 'react-router-dom';
import { Globe, Mail, Phone, MapPin, Twitter, Linkedin, Github, Facebook, ArrowUp } from 'lucide-react';

export default function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-secondary-900 text-white relative">
      {/* Top wave */}
      <div className="absolute top-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 0L60 10C120 20 240 40 360 50C480 60 600 60 720 55C840 50 960 40 1080 35C1200 30 1320 30 1380 30L1440 30V0H1380C1320 0 1200 0 1080 0C960 0 840 0 720 0C600 0 480 0 360 0C240 0 120 0 60 0H0Z"
            fill="white"
          />
        </svg>
      </div>

      <div className="container-custom pt-20 pb-8">
        {/* Main Footer Content */}
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-display font-bold">GlobalConnect</span>
            </div>
            <p className="text-secondary-300 mb-6 leading-relaxed">
              Connecting businesses worldwide through innovative project management and collaboration solutions.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200">
                <Github className="w-5 h-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-colors duration-200">
                <Facebook className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-display font-semibold text-lg mb-6">Platform</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/services" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Features
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Pricing
                </Link>
              </li>
              <li>
                <Link to="/security" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Security
                </Link>
              </li>
              <li>
                <Link to="/api-docs" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  API Docs
                </Link>
              </li>
              <li>
                <Link to="/integrations" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Integrations
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-display font-semibold text-lg mb-6">Resources</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/help" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Help Center
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Blog
                </Link>
              </li>
              <li>
                <Link to="/community" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Community
                </Link>
              </li>
              <li>
                <Link to="/success-stories" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Success Stories
                </Link>
              </li>
              <li>
                <Link to="/webinars" className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-primary-600 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                  Webinars
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-display font-semibold text-lg mb-6">Get in Touch</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-secondary-800 rounded-lg flex items-center justify-center">
                  <Mail className="w-4 h-4 text-primary-400" />
                </div>
                <span className="text-secondary-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-secondary-800 rounded-lg flex items-center justify-center">
                  <Phone className="w-4 h-4 text-primary-400" />
                </div>
                <span className="text-secondary-300">+****************</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-secondary-800 rounded-lg flex items-center justify-center">
                  <MapPin className="w-4 h-4 text-primary-400" />
                </div>
                <span className="text-secondary-300">San Francisco, CA</span>
              </div>
            </div>

            {/* Newsletter */}
            <div className="mt-8">
              <h4 className="font-semibold mb-3">Stay Updated</h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 bg-secondary-800 border border-secondary-700 rounded-l-lg focus:outline-none focus:border-primary-500 text-white placeholder-secondary-400"
                />
                <button className="px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-r-lg transition-colors duration-200">
                  <Mail className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-secondary-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 mb-4 md:mb-0">
            <p className="text-secondary-400 text-sm">
              © 2024 GlobalConnect. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link to="/privacy" className="text-secondary-400 hover:text-primary-400 transition-colors duration-200">Privacy Policy</Link>
              <Link to="/terms" className="text-secondary-400 hover:text-primary-400 transition-colors duration-200">Terms of Service</Link>
              <Link to="/cookie-policy" className="text-secondary-400 hover:text-primary-400 transition-colors duration-200">Cookie Policy</Link>
            </div>
          </div>

          {/* Back to Top */}
          <button
            onClick={scrollToTop}
            className="w-10 h-10 bg-primary-600 hover:bg-primary-700 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
          >
            <ArrowUp className="w-5 h-5" />
          </button>
        </div>
      </div>
    </footer>
  );
}