import axios from 'axios';
import { MockDataService, MockUser } from './mockDataService';

const API_URL = 'http://localhost:5000/api/auth';
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || false; // Default to false to use real backend

// Configure axios defaults
axios.defaults.withCredentials = true;

// Flag to prevent infinite loops during logout
let isLoggingOut = false;

export interface User {
  _id: string;
  name: string;
  email: string;
  role: 'client' | 'vendor' | 'admin';
  company?: string;
  companyLogo?: string;
  bio?: string;
  location?: string;
  website?: string;
  skills?: string[];
  rating?: number;
}

export interface AuthResponse {
  _id: string;
  name: string;
  email: string;
  role: 'client' | 'vendor' | 'admin';
  company?: string;
  companyLogo?: string;
  token: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: 'client' | 'vendor';
  company?: string;
}

class AuthService {
  private token: string | null = null;
  private user: User | null = null;
  private interceptorId: number | null = null;

  constructor() {
    // Check for existing token in localStorage
    this.token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        this.user = JSON.parse(userData);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        this.clearAuth();
      }
    }

    // Set axios default authorization header if token exists
    if (this.token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
    }

    // Setup axios interceptors
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Remove existing interceptor if any
    if (this.interceptorId !== null) {
      axios.interceptors.response.eject(this.interceptorId);
    }

    // Add response interceptor to handle 401 errors
    this.interceptorId = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        // Only handle 401 errors and prevent infinite loops
        if (error.response?.status === 401 && !isLoggingOut) {
          console.log('401 Unauthorized - clearing auth and redirecting to login');
          isLoggingOut = true;
          this.clearAuth();

          // Reset the flag after a short delay
          setTimeout(() => {
            isLoggingOut = false;
          }, 1000);

          // Optionally trigger a custom event that components can listen to
          window.dispatchEvent(new CustomEvent('auth:unauthorized'));
        }
        return Promise.reject(error);
      }
    );
  }

  async login(data: LoginData): Promise<AuthResponse> {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user by email
      const user = MockDataService.getUsers().find(u => u.email === data.email);

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // For demo purposes, accept any password for existing users
      // In real app, you'd verify the password hash
      const mockToken = `mock_token_${user._id}_${Date.now()}`;

      const authData: AuthResponse = {
        token: mockToken,
        user: user as User
      };

      this.setAuth(authData);
      return authData;
    }

    try {
      const response = await axios.post(`${API_URL}/login`, data);
      const authData = response.data;

      this.setAuth(authData);
      return authData;
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    if (USE_MOCK_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1200));

      // Check if user already exists
      const existingUser = MockDataService.getUsers().find(u => u.email === data.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser: MockUser = {
        _id: `user_${Date.now()}`,
        name: data.name,
        email: data.email,
        role: data.role,
        company: data.company,
        location: data.location || '',
        bio: '',
        skills: data.skills || [],
        rating: 0,
        avatar: `https://images.unsplash.com/photo-${Math.floor(Math.random() * 1000000)}?auto=format&fit=crop&w=400&q=80`,
        createdAt: new Date().toISOString()
      };

      const mockToken = `mock_token_${newUser._id}_${Date.now()}`;

      const authData: AuthResponse = {
        token: mockToken,
        user: newUser as User
      };

      this.setAuth(authData);
      return authData;
    }

    try {
      const response = await axios.post(`${API_URL}/register`, data);
      const authData = response.data;

      this.setAuth(authData);
      return authData;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  }

  async getProfile(): Promise<User> {
    try {
      const response = await axios.get(`${API_URL}/profile`);
      this.user = response.data;
      localStorage.setItem('user', JSON.stringify(this.user));
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to get profile');
    }
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/profile`, data);
      const updatedUser = response.data;
      
      this.user = updatedUser;
      localStorage.setItem('user', JSON.stringify(this.user));
      
      // Update token if provided
      if (updatedUser.token) {
        this.token = updatedUser.token;
        localStorage.setItem('token', this.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
      
      return updatedUser;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update profile');
    }
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<void> {
    try {
      await axios.put(`${API_URL}/change-password`, data);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to change password');
    }
  }

  async forgotPassword(email: string): Promise<void> {
    try {
      await axios.post(`${API_URL}/forgot-password`, { email });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send reset email');
    }
  }

  async resetPassword(token: string, password: string): Promise<void> {
    try {
      await axios.post(`${API_URL}/reset-password`, { token, password });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to reset password');
    }
  }

  async verifyEmail(token: string): Promise<void> {
    try {
      await axios.post(`${API_URL}/verify-email`, { token });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to verify email');
    }
  }

  async verifyToken(): Promise<boolean> {
    if (!this.token) return false;

    try {
      const response = await axios.get(`${API_URL}/verify`);
      return response.data.isValid;
    } catch (error) {
      this.clearAuth();
      return false;
    }
  }

  logout(): void {
    this.clearAuth();
  }

  private setAuth(authData: AuthResponse): void {
    this.token = authData.token;
    this.user = {
      _id: authData._id,
      name: authData.name,
      email: authData.email,
      role: authData.role,
      company: authData.company,
      companyLogo: authData.companyLogo
    };

    localStorage.setItem('token', this.token);
    localStorage.setItem('user', JSON.stringify(this.user));
    axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
  }

  private clearAuth(): void {
    this.token = null;
    this.user = null;
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete axios.defaults.headers.common['Authorization'];

    // Trigger a custom event for components to react to logout
    window.dispatchEvent(new CustomEvent('auth:logout'));
  }

  getCurrentUser(): User | null {
    return this.user;
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token && !!this.user;
  }

  setAuthHeader(): void {
    const token = this.getToken();
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }
}

export const authService = new AuthService();
