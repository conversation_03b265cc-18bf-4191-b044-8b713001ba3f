import React from 'react';
import { Puzzle, Zap, Settings, CheckCircle } from 'lucide-react';

export default function Integrations() {
  const integrations = [
    {
      name: 'Slack',
      description: 'Get project updates and notifications directly in your Slack channels',
      logo: '🔔',
      category: 'Communication',
      status: 'Available'
    },
    {
      name: 'Google Drive',
      description: 'Sync project documents and files with your Google Drive',
      logo: '📁',
      category: 'Storage',
      status: 'Available'
    },
    {
      name: 'Strip<PERSON>',
      description: 'Process payments and manage billing seamlessly',
      logo: '💳',
      category: 'Payments',
      status: 'Available'
    },
    {
      name: 'Zoom',
      description: 'Schedule and join project meetings directly from the platform',
      logo: '📹',
      category: 'Video Conferencing',
      status: 'Available'
    },
    {
      name: '<PERSON><PERSON>',
      description: 'Sync project tasks and issues with your Jira workspace',
      logo: '📋',
      category: 'Project Management',
      status: 'Coming Soon'
    },
    {
      name: 'GitHub',
      description: 'Connect repositories and track development progress',
      logo: '🐙',
      category: 'Development',
      status: 'Coming Soon'
    }
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-purple-600 to-blue-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Puzzle className="w-16 h-16 mx-auto mb-6 text-purple-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Integrations
            </h1>
            <p className="text-xl text-purple-100 leading-relaxed">
              Connect GlobalConnect with your favorite tools and streamline your workflow. Build a unified ecosystem for your projects.
            </p>
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Why Use Integrations?
              </h2>
              <p className="text-lg text-secondary-600">
                Seamlessly connect your existing tools and workflows
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <div className="card p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Automate Workflows
                </h3>
                <p className="text-secondary-600">
                  Reduce manual work by automating repetitive tasks across your tools.
                </p>
              </div>

              <div className="card p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Settings className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Centralize Data
                </h3>
                <p className="text-secondary-600">
                  Keep all your project data synchronized across different platforms.
                </p>
              </div>

              <div className="card p-6 text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  Improve Efficiency
                </h3>
                <p className="text-secondary-600">
                  Work faster by eliminating context switching between applications.
                </p>
              </div>
            </div>

            {/* Available Integrations */}
            <div>
              <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
                Available Integrations
              </h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {integrations.map((integration, index) => (
                  <div key={index} className="card p-6 hover:shadow-lg transition-shadow duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-3xl">{integration.logo}</div>
                        <div>
                          <h3 className="text-lg font-semibold text-secondary-900">
                            {integration.name}
                          </h3>
                          <span className="text-sm text-secondary-500">
                            {integration.category}
                          </span>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        integration.status === 'Available' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {integration.status}
                      </span>
                    </div>
                    
                    <p className="text-secondary-600 mb-4">
                      {integration.description}
                    </p>
                    
                    <button 
                      className={`w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${
                        integration.status === 'Available'
                          ? 'bg-primary-600 hover:bg-primary-700 text-white'
                          : 'bg-secondary-200 text-secondary-500 cursor-not-allowed'
                      }`}
                      disabled={integration.status !== 'Available'}
                    >
                      {integration.status === 'Available' ? 'Connect' : 'Coming Soon'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Integrations */}
      <div className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-secondary-900 mb-6">
              Need a Custom Integration?
            </h2>
            <p className="text-lg text-secondary-600 mb-8">
              Don't see the integration you need? Our API makes it easy to build custom connections, or we can help you create one.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                View API Documentation
              </button>
              <button className="btn-secondary">
                Request Integration
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Getting Started */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              Getting Started
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary-600 font-bold">1</span>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                  Choose Integration
                </h3>
                <p className="text-secondary-600">
                  Select the tool you want to connect from our available integrations.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary-600 font-bold">2</span>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                  Authenticate
                </h3>
                <p className="text-secondary-600">
                  Securely connect your accounts using OAuth or API keys.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-primary-600 font-bold">3</span>
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                  Configure
                </h3>
                <p className="text-secondary-600">
                  Set up your preferences and start automating your workflows.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
