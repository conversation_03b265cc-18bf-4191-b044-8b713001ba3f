import { 
  connectDB, 
  clearData, 
  seedUsers, 
  seedProjects, 
  seedBids,
  demoUsers,
  demoProjects 
} from './demoDataSeeder.js';
import { seedConversationsAndMessages } from './messageSeeder.js';
import { seedDocuments } from './documentSeeder.js';
import { seedNotifications } from './notificationSeeder.js';

// Main seeding function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...\n');
    
    // Connect to database
    await connectDB();
    
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await clearData();
    
    // Seed users
    console.log('👥 Seeding users...');
    const users = await seedUsers();
    
    // Seed projects
    console.log('📋 Seeding projects...');
    const projects = await seedProjects(users);
    
    // Seed bids
    console.log('💰 Seeding bids...');
    const bids = await seedBids(projects, users);
    
    // Seed conversations and messages
    console.log('💬 Seeding conversations and messages...');
    await seedConversationsAndMessages(users, projects);
    
    // Seed documents
    console.log('📄 Seeding documents...');
    await seedDocuments(projects, users);
    
    // Seed notifications
    console.log('🔔 Seeding notifications...');
    await seedNotifications(users, projects, bids);
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Users: ${users.length}`);
    console.log(`   Projects: ${projects.length}`);
    console.log(`   Bids: ${bids.length}`);
    
    console.log('\n🔐 Demo Login Credentials:');
    console.log('   Client: <EMAIL> / password123');
    console.log('   Vendor: <EMAIL> / password123');
    console.log('   Admin: <EMAIL> / admin123');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (process.argv[1].endsWith('seedDatabase.js')) {
  seedDatabase();
}

export default seedDatabase;
