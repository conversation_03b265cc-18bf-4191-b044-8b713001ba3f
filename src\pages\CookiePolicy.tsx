import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ting<PERSON>, Shield, Info, CheckCircle, X } from 'lucide-react';

export default function CookiePolicy() {
  const [cookieSettings, setCookieSettings] = useState({
    necessary: true,
    analytics: true,
    marketing: false,
    preferences: true
  });

  const handleCookieToggle = (type: string) => {
    if (type === 'necessary') return; // Necessary cookies cannot be disabled
    setCookieSettings(prev => ({
      ...prev,
      [type]: !prev[type as keyof typeof prev]
    }));
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-amber-600 to-orange-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Cookie className="w-16 h-16 mx-auto mb-6 text-amber-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Cookie Policy
            </h1>
            <p className="text-xl text-amber-100 leading-relaxed">
              Learn about how we use cookies to improve your experience on GlobalConnect and how you can manage your preferences.
            </p>
          </div>
        </div>
      </div>

      {/* Cookie Settings Panel */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="card p-8 mb-12">
              <div className="flex items-center mb-6">
                <Settings className="w-6 h-6 text-primary-600 mr-3" />
                <h2 className="text-2xl font-bold text-secondary-900">
                  Cookie Preferences
                </h2>
              </div>
              
              <p className="text-secondary-600 mb-8">
                Manage your cookie preferences below. You can enable or disable different types of cookies based on your preferences.
              </p>

              <div className="space-y-6">
                {[
                  {
                    type: 'necessary',
                    title: 'Necessary Cookies',
                    description: 'These cookies are essential for the website to function properly. They cannot be disabled.',
                    required: true
                  },
                  {
                    type: 'analytics',
                    title: 'Analytics Cookies',
                    description: 'Help us understand how visitors interact with our website by collecting anonymous information.',
                    required: false
                  },
                  {
                    type: 'marketing',
                    title: 'Marketing Cookies',
                    description: 'Used to track visitors across websites to display relevant advertisements.',
                    required: false
                  },
                  {
                    type: 'preferences',
                    title: 'Preference Cookies',
                    description: 'Remember your preferences and settings to provide a personalized experience.',
                    required: false
                  }
                ].map((cookie) => (
                  <div key={cookie.type} className="flex items-start justify-between p-4 border border-secondary-200 rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-semibold text-secondary-900 mb-2">
                        {cookie.title}
                        {cookie.required && (
                          <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                            Required
                          </span>
                        )}
                      </h3>
                      <p className="text-sm text-secondary-600">
                        {cookie.description}
                      </p>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleCookieToggle(cookie.type)}
                        disabled={cookie.required}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                          cookieSettings[cookie.type as keyof typeof cookieSettings]
                            ? 'bg-primary-600'
                            : 'bg-secondary-300'
                        } ${cookie.required ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                            cookieSettings[cookie.type as keyof typeof cookieSettings]
                              ? 'translate-x-6'
                              : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <button className="btn-primary">
                  Save Preferences
                </button>
                <button className="btn-secondary">
                  Accept All Cookies
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* What Are Cookies */}
      <div className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              What Are Cookies?
            </h2>
            
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Info className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Definition
                  </h3>
                  <p className="text-secondary-600">
                    Cookies are small text files that are stored on your device when you visit a website. They help websites remember information about your visit, which can make it easier to visit the site again and make the site more useful to you.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    How We Use Cookies
                  </h3>
                  <p className="text-secondary-600 mb-4">
                    We use cookies to enhance your browsing experience, analyze site traffic, and personalize content. Here's how different types of cookies work:
                  </p>
                  <ul className="space-y-2 text-secondary-600">
                    <li>• <strong>Session Cookies:</strong> Temporary cookies that expire when you close your browser</li>
                    <li>• <strong>Persistent Cookies:</strong> Remain on your device for a set period or until deleted</li>
                    <li>• <strong>First-party Cookies:</strong> Set by our website directly</li>
                    <li>• <strong>Third-party Cookies:</strong> Set by external services we use</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <Shield className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    Your Control
                  </h3>
                  <p className="text-secondary-600">
                    You have full control over cookies. You can accept or decline cookies, and you can also modify your browser settings to automatically decline cookies if you prefer. However, please note that disabling cookies may affect the functionality of our website.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cookie Types Detail */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              Types of Cookies We Use
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Strictly Necessary Cookies
                </h3>
                <p className="text-secondary-600 mb-4">
                  These cookies are essential for you to browse the website and use its features.
                </p>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Authentication and security</li>
                  <li>• Shopping cart functionality</li>
                  <li>• Form submission</li>
                  <li>• Load balancing</li>
                </ul>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Performance Cookies
                </h3>
                <p className="text-secondary-600 mb-4">
                  These cookies collect information about how you use our website.
                </p>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Page visit statistics</li>
                  <li>• Error tracking</li>
                  <li>• Performance monitoring</li>
                  <li>• User behavior analysis</li>
                </ul>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Functionality Cookies
                </h3>
                <p className="text-secondary-600 mb-4">
                  These cookies allow the website to remember choices you make.
                </p>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Language preferences</li>
                  <li>• Theme settings</li>
                  <li>• Location data</li>
                  <li>• Accessibility options</li>
                </ul>
              </div>

              <div className="card p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                  Targeting Cookies
                </h3>
                <p className="text-secondary-600 mb-4">
                  These cookies are used to deliver advertisements relevant to you.
                </p>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Personalized advertising</li>
                  <li>• Social media integration</li>
                  <li>• Cross-site tracking</li>
                  <li>• Retargeting campaigns</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact */}
      <div className="py-16 bg-secondary-900 text-white">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Questions About Cookies?
            </h2>
            <p className="text-secondary-300 mb-8">
              If you have any questions about our use of cookies, please don't hesitate to contact us.
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="btn-primary inline-flex items-center"
            >
              Contact Privacy Team
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
