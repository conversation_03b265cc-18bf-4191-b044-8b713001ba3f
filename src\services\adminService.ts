import axios from 'axios';
import { User } from './authService';
import { Project } from './projectService';
import { Bid } from './bidService';

const API_URL = 'http://localhost:5001/api';

export interface AdminStats {
  users: {
    total: number;
    clients: number;
    vendors: number;
    admins: number;
    newThisMonth: number;
  };
  projects: {
    total: number;
    open: number;
    inProgress: number;
    completed: number;
    cancelled: number;
    newThisMonth: number;
  };
  bids: {
    total: number;
    pending: number;
    accepted: number;
    rejected: number;
    newThisMonth: number;
  };
  revenue: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
}

export interface UserManagement {
  users: User[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export interface ProjectManagement {
  projects: Project[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export interface SystemActivity {
  recentUsers: User[];
  recentProjects: Project[];
  recentBids: Bid[];
  systemAlerts: Array<{
    id: string;
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }>;
}

class AdminService {
  // Dashboard Statistics
  async getDashboardStats(): Promise<AdminStats> {
    try {
      const response = await axios.get(`${API_URL}/analytics/platform`);
      const platformData = response.data;

      // Calculate growth rates and additional metrics
      const currentMonth = new Date().getMonth();
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;

      return {
        users: {
          total: platformData.users.total,
          clients: platformData.users.clients,
          vendors: platformData.users.vendors,
          admins: platformData.users.total - platformData.users.clients - platformData.users.vendors,
          newThisMonth: this.calculateNewThisMonth(platformData.users.overTime)
        },
        projects: {
          total: platformData.projects.total,
          open: platformData.projects.open,
          inProgress: platformData.projects.inProgress,
          completed: platformData.projects.completed,
          cancelled: platformData.projects.total - platformData.projects.open - platformData.projects.inProgress - platformData.projects.completed,
          newThisMonth: this.calculateNewThisMonth(platformData.projects.overTime)
        },
        bids: {
          total: platformData.bids.total,
          pending: Math.round(platformData.bids.total * 0.3), // Estimated
          accepted: platformData.bids.accepted,
          rejected: platformData.bids.total - platformData.bids.accepted,
          newThisMonth: Math.round(platformData.bids.total * 0.1) // Estimated
        },
        revenue: {
          total: 0, // Would need revenue endpoint
          thisMonth: 0,
          lastMonth: 0,
          growth: 0
        }
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
  }

  // User Management
  async getUsers(page: number = 1, limit: number = 20, search?: string, role?: string): Promise<UserManagement> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (search) params.append('search', search);
      if (role && role !== 'all') params.append('role', role);

      const response = await axios.get(`${API_URL}/users?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/users/${userId}`, userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/users/${userId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  async suspendUser(userId: string, reason: string): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/users/${userId}`, {
        isActive: false,
        suspensionReason: reason
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to suspend user');
    }
  }

  async activateUser(userId: string): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/users/${userId}`, {
        isActive: true,
        suspensionReason: null
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to activate user');
    }
  }

  // Project Management
  async getProjects(page: number = 1, limit: number = 20, status?: string): Promise<ProjectManagement> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (status && status !== 'all') params.append('status', status);

      const response = await axios.get(`${API_URL}/projects?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch projects');
    }
  }

  async updateProjectStatus(projectId: string, status: string, reason?: string): Promise<Project> {
    try {
      const response = await axios.put(`${API_URL}/projects/${projectId}`, {
        status,
        adminNote: reason
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update project status');
    }
  }

  async deleteProject(projectId: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/projects/${projectId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete project');
    }
  }

  // System Activity
  async getSystemActivity(): Promise<SystemActivity> {
    try {
      const [usersResponse, projectsResponse] = await Promise.all([
        axios.get(`${API_URL}/users?limit=5&sort=createdAt`),
        axios.get(`${API_URL}/projects?limit=5&sort=createdAt`)
      ]);

      return {
        recentUsers: usersResponse.data.users || [],
        recentProjects: projectsResponse.data.projects || [],
        recentBids: [], // Would need bids endpoint
        systemAlerts: [
          {
            id: '1',
            type: 'info',
            message: 'System backup completed successfully',
            timestamp: new Date().toISOString()
          },
          {
            id: '2',
            type: 'warning',
            message: 'High server load detected',
            timestamp: new Date(Date.now() - 3600000).toISOString()
          }
        ]
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch system activity');
    }
  }

  // Utility Methods
  private calculateNewThisMonth(overTimeData: Array<{ date: string; count?: number; total?: number }>): number {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    return overTimeData
      .filter(item => {
        const date = new Date(item.date);
        return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
      })
      .reduce((sum, item) => sum + (item.count || item.total || 0), 0);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatNumber(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  getUserStatusColor(user: User): string {
    if (!user.isActive) return 'text-error-600';
    if (user.isVerified) return 'text-success-600';
    return 'text-warning-600';
  }

  getUserStatusLabel(user: User): string {
    if (!user.isActive) return 'Suspended';
    if (user.isVerified) return 'Active';
    return 'Pending';
  }

  getProjectStatusColor(status: string): string {
    const colors = {
      open: 'text-blue-600',
      'in-progress': 'text-yellow-600',
      completed: 'text-green-600',
      cancelled: 'text-red-600',
      review: 'text-purple-600'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600';
  }
}

export const adminService = new AdminService();
