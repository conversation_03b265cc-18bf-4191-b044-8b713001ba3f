#!/usr/bin/env node

/**
 * Comprehensive Feature Test Suite for GlobalConnect Platform
 * Tests all demo features listed in the demo guide
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
const TEST_RESULTS = [];

// Demo credentials from DEMO_DATA_INFO.md
const DEMO_USERS = {
  clients: [
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' }
  ],
  vendors: [
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'password123', name: 'James Wilson' }
  ],
  admin: { email: '<EMAIL>', password: 'admin123', name: 'Admin User' }
};

// Test utilities
class TestRunner {
  constructor() {
    this.tokens = {};
    this.testResults = [];
    this.currentTest = null;
  }

  async log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    
    if (this.currentTest) {
      this.currentTest.logs.push({ timestamp, type, message });
    }
  }

  startTest(testName, description) {
    this.currentTest = {
      name: testName,
      description,
      startTime: new Date(),
      logs: [],
      passed: false,
      errors: []
    };
    this.log(`Starting test: ${testName} - ${description}`, 'test');
  }

  endTest(passed = true, error = null) {
    if (!this.currentTest) return;
    
    this.currentTest.endTime = new Date();
    this.currentTest.duration = this.currentTest.endTime - this.currentTest.startTime;
    this.currentTest.passed = passed;
    
    if (error) {
      this.currentTest.errors.push(error);
      this.log(`Test failed: ${error}`, 'error');
    }
    
    this.testResults.push(this.currentTest);
    this.log(`Test ${passed ? 'PASSED' : 'FAILED'}: ${this.currentTest.name}`, passed ? 'success' : 'error');
    this.currentTest = null;
  }

  async authenticate(userType, userIndex = 0) {
    try {
      let user;
      if (userType === 'admin') {
        user = DEMO_USERS.admin;
      } else {
        user = DEMO_USERS[userType][userIndex];
      }

      const response = await axios.post(`${BASE_URL}/auth/signin`, {
        email: user.email,
        password: user.password
      });

      if (response.data.token) {
        this.tokens[`${userType}_${userIndex}`] = response.data.token;
        this.log(`Authenticated as ${user.name} (${userType})`, 'success');
        return response.data;
      }
      throw new Error('No token received');
    } catch (error) {
      this.log(`Authentication failed for ${userType}: ${error.message}`, 'error');
      throw error;
    }
  }

  getAuthHeaders(userType, userIndex = 0) {
    const token = this.tokens[`${userType}_${userIndex}`];
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  async makeRequest(method, endpoint, data = null, userType = 'clients', userIndex = 0) {
    try {
      const config = {
        method,
        url: `${BASE_URL}${endpoint}`,
        headers: this.getAuthHeaders(userType, userIndex)
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      this.log(`Request failed: ${method} ${endpoint} - ${error.message}`, 'error');
      throw error;
    }
  }

  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(t => t.passed).length;
    const failedTests = totalTests - passedTests;
    
    const report = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0
      },
      tests: this.testResults,
      generatedAt: new Date().toISOString()
    };

    // Save detailed report
    fs.writeFileSync(
      path.join(__dirname, 'test-report.json'),
      JSON.stringify(report, null, 2)
    );

    // Generate summary report
    const summaryReport = `
# GlobalConnect Feature Test Report

## Summary
- **Total Tests**: ${totalTests}
- **Passed**: ${passedTests}
- **Failed**: ${failedTests}
- **Success Rate**: ${report.summary.successRate}%

## Test Results
${this.testResults.map(test => `
### ${test.name}
- **Status**: ${test.passed ? '✅ PASSED' : '❌ FAILED'}
- **Duration**: ${test.duration}ms
- **Description**: ${test.description}
${test.errors.length > 0 ? `- **Errors**: ${test.errors.join(', ')}` : ''}
`).join('')}

Generated at: ${report.generatedAt}
`;

    fs.writeFileSync(
      path.join(__dirname, 'test-report.md'),
      summaryReport
    );

    console.log('\n' + '='.repeat(60));
    console.log('TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${report.summary.successRate}%`);
    console.log('='.repeat(60));

    return report;
  }
}

// Export for use in other test files
module.exports = { TestRunner, DEMO_USERS, BASE_URL };

// Main execution
if (require.main === module) {
  console.log('GlobalConnect Comprehensive Feature Test Suite');
  console.log('This is the base test runner. Run specific test files to execute tests.');
}
