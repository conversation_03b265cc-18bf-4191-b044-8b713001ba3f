import React, { useState } from 'react';
import {
  Play,
  User,
  Briefcase,
  MessageCircle,
  FileText,
  BarChart3,
  Settings,
  Eye,
  Copy,
  CheckCircle,
  ArrowRight,
  Users,
  Crown,
  Zap
} from 'lucide-react';

interface DemoGuideProps {
  onStartDemo?: () => void;
}

export default function DemoGuide({ onStartDemo }: DemoGuideProps = {}) {
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedCredential, setCopiedCredential] = useState<string | null>(null);

  const demoCredentials = {
    clients: [
      {
        name: "<PERSON>",
        title: "CEO of TechCorp Solutions",
        email: "<EMAIL>",
        password: "password123",
        specialization: "Digital transformation for enterprises",
        avatar: "👩‍💼"
      },
      {
        name: "<PERSON>",
        title: "Founder of Innovate Digital",
        email: "<EMAIL>",
        password: "password123",
        specialization: "Cutting-edge web technologies",
        avatar: "👨‍💻"
      },
      {
        name: "<PERSON>",
        title: "Product Manager at NextGen Startup",
        email: "<EMAIL>",
        password: "password123",
        specialization: "Mobile-first solutions",
        avatar: "👩‍🚀"
      }
    ],
    vendors: [
      {
        name: "<PERSON>",
        title: "Full-stack Developer",
        email: "<EMAIL>",
        password: "password123",
        skills: "React, Node.js, MongoDB, AWS, TypeScript",
        rating: "4.9/5",
        avatar: "👨‍💻"
      },
      {
        name: "Maria Garcia",
        title: "UI/UX Designer",
        email: "<EMAIL>",
        password: "password123",
        skills: "UI/UX Design, Figma, Adobe Creative Suite",
        rating: "4.8/5",
        avatar: "👩‍🎨"
      },
      {
        name: "David Kim",
        title: "Mobile App Developer",
        email: "<EMAIL>",
        password: "password123",
        skills: "React Native, Flutter, iOS, Android",
        rating: "4.7/5",
        avatar: "📱"
      }
    ],
    admin: {
      name: "Admin User",
      title: "Platform Administrator",
      email: "<EMAIL>",
      password: "admin123",
      access: "Full platform management capabilities",
      avatar: "👑"
    }
  };

  const features = [
    {
      icon: Briefcase,
      title: "Project Management",
      description: "Create, browse, and manage projects with detailed specifications",
      steps: [
        "Browse 8+ demo projects across different categories",
        "Create new projects with budget and timeline",
        "Manage project status and milestones",
        "View project analytics and performance"
      ]
    },
    {
      icon: Users,
      title: "Bidding System",
      description: "Submit competitive bids and manage proposals",
      steps: [
        "View 20+ realistic bids on various projects",
        "Submit detailed proposals with attachments",
        "Accept or reject bids as a client",
        "Track bid status and negotiations"
      ]
    },
    {
      icon: MessageCircle,
      title: "Real-time Messaging",
      description: "Communicate with team members and clients instantly",
      steps: [
        "Send messages with file attachments",
        "Create project-specific conversations",
        "Real-time message delivery and read receipts",
        "Group conversations for team collaboration"
      ]
    },
    {
      icon: FileText,
      title: "Document Management",
      description: "Secure file sharing with version control",
      steps: [
        "Upload documents with security levels",
        "Version control and change tracking",
        "Role-based access permissions",
        "30+ demo documents across file types"
      ]
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track performance and project insights",
      steps: [
        "View personalized dashboard statistics",
        "Project performance analytics",
        "Revenue and earnings tracking",
        "User activity and engagement metrics"
      ]
    },
    {
      icon: Settings,
      title: "Admin Panel",
      description: "Complete platform management and oversight",
      steps: [
        "User management and role assignment",
        "Platform-wide analytics and reporting",
        "System configuration and settings",
        "Content moderation and security"
      ]
    }
  ];

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    setCopiedCredential(type);
    setTimeout(() => setCopiedCredential(null), 2000);
  };

  return (
    <section id="demo-guide" className="py-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
            <Play className="w-4 h-4 mr-2" />
            Try All Features Now
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-6">
            Explore Every Feature with <span className="gradient-text">Live Demo</span>
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Experience the full power of GlobalConnect with our comprehensive demo environment. 
            Use real demo accounts to test every feature and functionality.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap justify-center mb-12 bg-white rounded-xl p-2 shadow-soft max-w-2xl mx-auto">
          {[
            { id: 'overview', label: 'Overview', icon: Eye },
            { id: 'credentials', label: 'Demo Accounts', icon: User },
            { id: 'features', label: 'Features Guide', icon: Zap }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-primary-600 text-white shadow-medium'
                  : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <div className="card p-8">
                <div className="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
                  <BarChart3 className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                  Complete Demo Environment
                </h3>
                <p className="text-secondary-600 mb-6">
                  Our demo includes 9 realistic user accounts, 8+ projects, 20+ bids, 50+ messages, 
                  and 30+ documents to showcase every platform feature.
                </p>
                <ul className="space-y-2 text-secondary-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Real-time messaging and notifications
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Complete project lifecycle management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Advanced analytics and reporting
                  </li>
                </ul>
              </div>

              <div className="card p-8">
                <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6">
                  <Users className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                  Multiple User Perspectives
                </h3>
                <p className="text-secondary-600 mb-6">
                  Experience the platform from different viewpoints - clients posting projects, 
                  vendors submitting bids, and admins managing the platform.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                    <span className="font-medium">Clients</span>
                    <span className="text-primary-600 font-semibold">3 Accounts</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                    <span className="font-medium">Vendors</span>
                    <span className="text-blue-600 font-semibold">5 Accounts</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                    <span className="font-medium">Admin</span>
                    <span className="text-purple-600 font-semibold">1 Account</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <button 
                onClick={() => setActiveTab('credentials')}
                className="btn-primary inline-flex items-center text-lg px-8 py-4"
              >
                Get Demo Credentials
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Credentials Tab */}
        {activeTab === 'credentials' && (
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8 mb-12">
              {/* Clients */}
              <div className="card p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <Briefcase className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-secondary-900">Client Accounts</h3>
                    <p className="text-secondary-600">Project creators and business owners</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {demoCredentials.clients.map((client, index) => (
                    <div key={index} className="p-4 border border-secondary-200 rounded-lg hover:border-primary-300 transition-colors duration-200">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center">
                          <span className="text-2xl mr-3">{client.avatar}</span>
                          <div>
                            <h4 className="font-semibold text-secondary-900">{client.name}</h4>
                            <p className="text-sm text-secondary-600">{client.title}</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span className="text-secondary-600">Email:</span>
                          <div className="flex items-center">
                            <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                              {client.email}
                            </code>
                            <button
                              onClick={() => copyToClipboard(client.email, `client-email-${index}`)}
                              className="text-primary-600 hover:text-primary-700"
                            >
                              {copiedCredential === `client-email-${index}` ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-secondary-600">Password:</span>
                          <div className="flex items-center">
                            <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                              {client.password}
                            </code>
                            <button
                              onClick={() => copyToClipboard(client.password, `client-pass-${index}`)}
                              className="text-primary-600 hover:text-primary-700"
                            >
                              {copiedCredential === `client-pass-${index}` ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-secondary-500 mt-3 italic">{client.specialization}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Vendors */}
              <div className="card p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <User className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-secondary-900">Vendor Accounts</h3>
                    <p className="text-secondary-600">Freelancers and service providers</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {demoCredentials.vendors.slice(0, 3).map((vendor, index) => (
                    <div key={index} className="p-4 border border-secondary-200 rounded-lg hover:border-blue-300 transition-colors duration-200">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center">
                          <span className="text-2xl mr-3">{vendor.avatar}</span>
                          <div>
                            <h4 className="font-semibold text-secondary-900">{vendor.name}</h4>
                            <p className="text-sm text-secondary-600">{vendor.title}</p>
                            <div className="flex items-center mt-1">
                              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                                ⭐ {vendor.rating}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span className="text-secondary-600">Email:</span>
                          <div className="flex items-center">
                            <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                              {vendor.email}
                            </code>
                            <button
                              onClick={() => copyToClipboard(vendor.email, `vendor-email-${index}`)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              {copiedCredential === `vendor-email-${index}` ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-secondary-600">Password:</span>
                          <div className="flex items-center">
                            <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                              {vendor.password}
                            </code>
                            <button
                              onClick={() => copyToClipboard(vendor.password, `vendor-pass-${index}`)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              {copiedCredential === `vendor-pass-${index}` ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-secondary-500 mt-3 italic">{vendor.skills}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Admin Account */}
            <div className="max-w-md mx-auto">
              <div className="card p-6 border-2 border-purple-200">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <Crown className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-secondary-900">Admin Account</h3>
                    <p className="text-secondary-600">Full platform access</p>
                  </div>
                </div>
                
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-secondary-600">Email:</span>
                    <div className="flex items-center">
                      <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                        {demoCredentials.admin.email}
                      </code>
                      <button
                        onClick={() => copyToClipboard(demoCredentials.admin.email, 'admin-email')}
                        className="text-purple-600 hover:text-purple-700"
                      >
                        {copiedCredential === 'admin-email' ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-secondary-600">Password:</span>
                    <div className="flex items-center">
                      <code className="bg-secondary-100 px-2 py-1 rounded text-xs mr-2">
                        {demoCredentials.admin.password}
                      </code>
                      <button
                        onClick={() => copyToClipboard(demoCredentials.admin.password, 'admin-pass')}
                        className="text-purple-600 hover:text-purple-700"
                      >
                        {copiedCredential === 'admin-pass' ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Features Tab */}
        {activeTab === 'features' && (
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="card p-6 hover:shadow-lg transition-shadow duration-300">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-secondary-600 mb-4">
                    {feature.description}
                  </p>
                  <ul className="space-y-2">
                    {feature.steps.map((step, stepIndex) => (
                      <li key={stepIndex} className="flex items-start text-sm text-secondary-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        {step}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="card p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Explore?
            </h3>
            <p className="text-secondary-600 mb-6">
              Use any of the demo credentials above to login and experience the full platform functionality.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={onStartDemo}
                className="btn-primary inline-flex items-center"
              >
                <Play className="w-4 h-4 mr-2" />
                Start Demo Now
              </button>
              <button
                onClick={() => setActiveTab('credentials')}
                className="btn-secondary inline-flex items-center"
              >
                <User className="w-4 h-4 mr-2" />
                View All Accounts
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
