import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar, 
  DollarSign, 
  User, 
  Clock,
  Eye,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { projectService, Project, ProjectFilters } from '../../services/projectService';
import { User as UserType } from '../../services/authService';

interface ProjectsListProps {
  user: UserType;
}

export default function ProjectsList({ user }: ProjectsListProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState<ProjectFilters>({
    page: 1,
    limit: 12
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    total: 0
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchProjects();
  }, [filters]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await projectService.getProjects(filters);
      setProjects(response.projects);
      setPagination({
        page: response.page,
        pages: response.pages,
        total: response.total
      });
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log('Searching for:', searchTerm);
  };

  const categories = projectService.getCategories();

  if (loading && projects.length === 0) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <div className="container-custom py-8">
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-secondary-600">Loading projects...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-display font-bold text-secondary-900 mb-2">
              {user.role === 'client' ? 'My Projects' : 'Available Projects'}
            </h1>
            <p className="text-secondary-600">
              {user.role === 'client' 
                ? 'Manage your project listings and track progress' 
                : 'Browse and bid on exciting projects from clients worldwide'
              }
            </p>
          </div>
          {user.role === 'client' && (
            <Link to="/projects/create" className="btn-primary mt-4 lg:mt-0">
              <Plus className="w-5 h-5 mr-2" />
              Create Project
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="card p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search projects..."
                  className="input-field pl-10 w-full"
                />
              </div>
            </form>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-secondary"
            >
              <Filter className="w-5 h-5 mr-2" />
              Filters
            </button>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-secondary-200">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Status
                  </label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                    className="input-field"
                  >
                    <option value="">All Statuses</option>
                    <option value="open">Open</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Category
                  </label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
                    className="input-field"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Budget Range */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Min Budget
                  </label>
                  <input
                    type="number"
                    value={filters.minBudget || ''}
                    onChange={(e) => handleFilterChange('minBudget', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="$0"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Max Budget
                  </label>
                  <input
                    type="number"
                    value={filters.maxBudget || ''}
                    onChange={(e) => handleFilterChange('maxBudget', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="$100,000"
                    className="input-field"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 rounded-lg">
            <p className="text-error-700">{error}</p>
          </div>
        )}

        {/* Projects Grid */}
        {projects.length === 0 && !loading ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-12 h-12 text-secondary-400" />
            </div>
            <h3 className="text-xl font-semibold text-secondary-900 mb-2">No projects found</h3>
            <p className="text-secondary-600 mb-6">
              {user.role === 'client' 
                ? "You haven't created any projects yet." 
                : "No projects match your current filters."
              }
            </p>
            {user.role === 'client' && (
              <Link to="/projects/create" className="btn-primary">
                <Plus className="w-5 h-5 mr-2" />
                Create Your First Project
              </Link>
            )}
          </div>
        ) : (
          <>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {projects.map((project) => (
                <ProjectCard 
                  key={project._id} 
                  project={project} 
                  user={user}
                  onUpdate={fetchProjects}
                />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-between">
                <p className="text-secondary-600">
                  Showing {((pagination.page - 1) * (filters.limit || 12)) + 1} to{' '}
                  {Math.min(pagination.page * (filters.limit || 12), pagination.total)} of{' '}
                  {pagination.total} projects
                </p>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  
                  {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                        page === pagination.page
                          ? 'bg-primary-600 text-white'
                          : 'text-secondary-600 hover:bg-secondary-100'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.pages}
                    className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// Project Card Component
interface ProjectCardProps {
  project: Project;
  user: UserType;
  onUpdate: () => void;
}

function ProjectCard({ project, user, onUpdate }: ProjectCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this project?')) return;
    
    try {
      setIsDeleting(true);
      await projectService.deleteProject(project._id);
      onUpdate();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setIsDeleting(false);
    }
  };

  const isOwner = user._id === project.client._id;

  return (
    <div className="card p-6 hover:shadow-medium transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-secondary-900 mb-2 line-clamp-2">
            {project.title}
          </h3>
          <div className="flex items-center space-x-2 mb-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${projectService.getStatusColor(project.status)}`}>
              {project.status.replace('-', ' ')}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${projectService.getCategoryColor(project.category)}`}>
              {projectService.getCategories().find(c => c.value === project.category)?.label}
            </span>
          </div>
        </div>
        
        {isOwner && (
          <div className="flex items-center space-x-2">
            <Link
              to={`/projects/${project._id}/edit`}
              className="p-2 text-secondary-400 hover:text-primary-600 transition-colors duration-200"
            >
              <Edit className="w-4 h-4" />
            </Link>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="p-2 text-secondary-400 hover:text-error-600 transition-colors duration-200 disabled:opacity-50"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Description */}
      <p className="text-secondary-600 text-sm mb-4 line-clamp-3">
        {project.description}
      </p>

      {/* Details */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center text-sm text-secondary-600">
          <DollarSign className="w-4 h-4 mr-2" />
          <span>{projectService.formatBudget(project.budget)}</span>
        </div>
        <div className="flex items-center text-sm text-secondary-600">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{projectService.formatDeadline(project.deadline)}</span>
        </div>
        <div className="flex items-center text-sm text-secondary-600">
          <User className="w-4 h-4 mr-2" />
          <span>{project.client.name}</span>
        </div>
        {project.bids && (
          <div className="flex items-center text-sm text-secondary-600">
            <Clock className="w-4 h-4 mr-2" />
            <span>{project.bids.length} bid{project.bids.length !== 1 ? 's' : ''}</span>
          </div>
        )}
      </div>

      {/* Skills */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-1">
          {project.skills.slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs"
            >
              {skill}
            </span>
          ))}
          {project.skills.length > 3 && (
            <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
              +{project.skills.length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <Link
          to={`/projects/${project._id}`}
          className="btn-secondary text-sm"
        >
          <Eye className="w-4 h-4 mr-2" />
          View Details
        </Link>
        
        {!isOwner && user.role === 'vendor' && project.status === 'open' && (
          <Link
            to={`/projects/${project._id}/bid`}
            className="btn-primary text-sm"
          >
            Submit Bid
          </Link>
        )}
      </div>
    </div>
  );
}
