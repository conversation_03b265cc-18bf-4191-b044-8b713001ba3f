import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { User } from '../../services/authService';

// Components
import Navigation from '../Navigation';
import Hero from '../Hero';
import Features from '../Features';
import DemoGuide from '../DemoGuide';
import Blog from '../Blog';
import Reviews from '../Reviews';
import Footer from '../Footer';
import Dashboard from '../Dashboard/Dashboard';
import Profile from '../Profile/Profile';
import Settings from '../Settings/Settings';
import ProjectsList from '../Projects/ProjectsList';
import CreateProject from '../Projects/CreateProject';
import SubmitBid from '../Bids/SubmitBid';
import Messages from '../Messages/Messages';
import Documents from '../Documents/Documents';
import AnalyticsDashboard from '../Analytics/AnalyticsDashboard';
import AdminDashboard from '../Admin/AdminDashboard';
import BillingDashboard from '../Billing/BillingDashboard';
import { NotificationContainer } from '../UI/Notifications';

// Import new pages
import About from '../../pages/About';
import Pricing from '../../pages/Pricing';
import Contact from '../../pages/Contact';
import HowItWorks from '../../pages/HowItWorks';
import Services from '../../pages/Services';
import PrivacyPolicy from '../../pages/PrivacyPolicy';
import TermsOfService from '../../pages/TermsOfService';
import HelpCenter from '../../pages/HelpCenter';
import BlogPage from '../../pages/BlogPage';
import Security from '../../pages/Security';
import ApiDocs from '../../pages/ApiDocs';
import Integrations from '../../pages/Integrations';
import Community from '../../pages/Community';
import SuccessStories from '../../pages/SuccessStories';
import Webinars from '../../pages/Webinars';
import CookiePolicy from '../../pages/CookiePolicy';
import DataTest from '../Test/DataTest';

interface AppRouterProps {
  isAuthenticated: boolean;
  currentUser: User | null;
  notifications: any[];
  onSignIn: () => void;
  onSignUp: () => void;
  onSignOut: () => void;
  onGetStarted: () => void;
  onTryDemo: () => void;
  onUserUpdate: (user: User) => void;
  removeNotification: (id: string) => void;
}

// Landing Page Component
const LandingPage: React.FC<{ onGetStarted: () => void; onTryDemo: () => void }> = ({ onGetStarted, onTryDemo }) => (
  <>
    <Hero onGetStarted={onGetStarted} onTryDemo={onTryDemo} />
    <Features />
    <DemoGuide />
    <Blog />
    <Reviews />
    <Footer />
  </>
);

// Protected Route Component
const ProtectedRoute: React.FC<{ 
  children: React.ReactNode; 
  isAuthenticated: boolean; 
}> = ({ children, isAuthenticated }) => {
  return isAuthenticated ? <>{children}</> : <Navigate to="/" replace />;
};

export default function AppRouter({
  isAuthenticated,
  currentUser,
  notifications,
  onSignIn,
  onSignUp,
  onSignOut,
  onGetStarted,
  onTryDemo,
  onUserUpdate,
  removeNotification
}: AppRouterProps) {
  return (
    <Router>
      <div className="min-h-screen bg-white">
        <Navigation
          isAuthenticated={isAuthenticated}
          user={currentUser}
          onSignIn={onSignIn}
          onSignUp={onSignUp}
          onSignOut={onSignOut}
        />
        
        <Routes>
          {/* Public Routes */}
          <Route
            path="/"
            element={
              isAuthenticated ?
                <Navigate to="/dashboard" replace /> :
                <LandingPage onGetStarted={onGetStarted} onTryDemo={onTryDemo} />
            }
          />

          {/* Public Pages */}
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Services />} />
          <Route path="/how-it-works" element={<HowItWorks />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/blog" element={<BlogPage />} />
          <Route path="/help" element={<HelpCenter />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/security" element={<Security />} />
          <Route path="/api-docs" element={<ApiDocs />} />
          <Route path="/integrations" element={<Integrations />} />
          <Route path="/community" element={<Community />} />
          <Route path="/success-stories" element={<SuccessStories />} />
          <Route path="/webinars" element={<Webinars />} />
          <Route path="/cookie-policy" element={<CookiePolicy />} />
          <Route path="/test" element={<DataTest />} />
          
          {/* Protected Routes */}
          <Route 
            path="/dashboard" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Dashboard userRole={currentUser?.role || 'client'} currentUser={currentUser} />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/profile" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && (
                  <Profile 
                    user={currentUser} 
                    onUserUpdate={onUserUpdate}
                  />
                )}
              </ProtectedRoute>
            } 
          />
          
          <Route
            path="/settings"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Settings user={currentUser} />
              </ProtectedRoute>
            }
          />

          <Route
            path="/analytics"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <AnalyticsDashboard user={currentUser} />}
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <AdminDashboard user={currentUser} />}
              </ProtectedRoute>
            }
          />

          <Route
            path="/billing"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <BillingDashboard user={currentUser} />}
              </ProtectedRoute>
            }
          />
          
          {/* Project routes */}
          <Route
            path="/projects"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <ProjectsList user={currentUser} />}
              </ProtectedRoute>
            }
          />

          <Route
            path="/projects/create"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <CreateProject />
              </ProtectedRoute>
            }
          />

          <Route
            path="/projects/:projectId/bid"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <SubmitBid />
              </ProtectedRoute>
            }
          />

          <Route
            path="/projects/:projectId/documents"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <Documents user={currentUser} />}
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/messages"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                {currentUser && <Messages user={currentUser} />}
              </ProtectedRoute>
            }
          />
          
          <Route 
            path="/bids" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
                  <div className="text-center">
                    <h1 className="text-3xl font-bold text-secondary-900 mb-4">My Bids</h1>
                    <p className="text-secondary-600">Coming soon...</p>
                  </div>
                </div>
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/admin" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
                  <div className="text-center">
                    <h1 className="text-3xl font-bold text-secondary-900 mb-4">Admin Panel</h1>
                    <p className="text-secondary-600">Coming soon...</p>
                  </div>
                </div>
              </ProtectedRoute>
            } 
          />
          
          {/* Catch all route */}
          <Route 
            path="*" 
            element={
              <div className="min-h-screen bg-secondary-50 flex items-center justify-center">
                <div className="text-center">
                  <h1 className="text-3xl font-bold text-secondary-900 mb-4">404 - Page Not Found</h1>
                  <p className="text-secondary-600 mb-6">The page you're looking for doesn't exist.</p>
                  <button 
                    onClick={() => window.history.back()}
                    className="btn-primary"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            } 
          />
        </Routes>
        
        <NotificationContainer
          notifications={notifications}
          onClose={removeNotification}
        />
      </div>
    </Router>
  );
}
