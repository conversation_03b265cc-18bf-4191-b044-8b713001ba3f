import React from 'react';
import { Star, TrendingUp, Users, DollarSign, Quote } from 'lucide-react';

export default function SuccessStories() {
  const stories = [
    {
      name: "<PERSON>",
      role: "Freelance Web Developer",
      image: "👩‍💻",
      story: "GlobalConnect helped me transition from a corporate job to freelancing. In my first year, I've completed 25+ projects and increased my income by 150%.",
      metrics: {
        projects: 25,
        income: "150%",
        rating: 4.9
      }
    },
    {
      name: "TechStart Inc.",
      role: "Startup Company",
      image: "🚀",
      story: "We found amazing developers through GlobalConnect who helped us build our MVP in just 3 months. The quality and communication were exceptional.",
      metrics: {
        projects: 8,
        savings: "40%",
        rating: 5.0
      }
    },
    {
      name: "<PERSON>",
      role: "Digital Marketing Expert",
      image: "👨‍💼",
      story: "The platform's project matching algorithm is incredible. I get relevant opportunities that match my skills perfectly, saving me hours of searching.",
      metrics: {
        projects: 42,
        income: "200%",
        rating: 4.8
      }
    }
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-orange-600 to-red-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Star className="w-16 h-16 mx-auto mb-6 text-orange-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Success Stories
            </h1>
            <p className="text-xl text-orange-100 leading-relaxed">
              Discover how freelancers and businesses are achieving their goals with GlobalConnect. Real stories from real people.
            </p>
          </div>
        </div>
      </div>

      {/* Platform Stats */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Platform Impact
              </h2>
              <p className="text-lg text-secondary-600">
                The numbers speak for themselves
              </p>
            </div>

            <div className="grid md:grid-cols-4 gap-8">
              <div className="card p-6 text-center">
                <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-secondary-900 mb-2">$50M+</div>
                <div className="text-secondary-600">Total Earnings</div>
              </div>
              <div className="card p-6 text-center">
                <Users className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-secondary-900 mb-2">10K+</div>
                <div className="text-secondary-600">Success Stories</div>
              </div>
              <div className="card p-6 text-center">
                <Star className="w-8 h-8 text-yellow-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-secondary-900 mb-2">4.8/5</div>
                <div className="text-secondary-600">Average Rating</div>
              </div>
              <div className="card p-6 text-center">
                <DollarSign className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <div className="text-3xl font-bold text-secondary-900 mb-2">95%</div>
                <div className="text-secondary-600">Project Success Rate</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Stories */}
      <div className="py-16 bg-white">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Featured Success Stories
              </h2>
              <p className="text-lg text-secondary-600">
                Inspiring journeys from our community members
              </p>
            </div>

            <div className="space-y-12">
              {stories.map((story, index) => (
                <div key={index} className="card p-8">
                  <div className="flex flex-col lg:flex-row items-start space-y-6 lg:space-y-0 lg:space-x-8">
                    <div className="flex-shrink-0">
                      <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center text-4xl">
                        {story.image}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-bold text-secondary-900">{story.name}</h3>
                          <p className="text-secondary-600">{story.role}</p>
                        </div>
                        <Quote className="w-8 h-8 text-primary-300" />
                      </div>
                      
                      <p className="text-lg text-secondary-700 mb-6 italic">
                        "{story.story}"
                      </p>
                      
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center p-3 bg-secondary-50 rounded-lg">
                          <div className="text-2xl font-bold text-primary-600">
                            {story.metrics.projects || story.metrics.savings}
                          </div>
                          <div className="text-sm text-secondary-600">
                            {story.metrics.projects ? 'Projects' : 'Cost Savings'}
                          </div>
                        </div>
                        <div className="text-center p-3 bg-secondary-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">
                            {story.metrics.income || story.metrics.savings}
                          </div>
                          <div className="text-sm text-secondary-600">
                            Income Growth
                          </div>
                        </div>
                        <div className="text-center p-3 bg-secondary-50 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-600">
                            {story.metrics.rating}
                          </div>
                          <div className="text-sm text-secondary-600">
                            Rating
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-secondary-900 mb-8 text-center">
              Success Across All Industries
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { category: "Web Development", count: "2,500+", icon: "💻" },
                { category: "Graphic Design", count: "1,800+", icon: "🎨" },
                { category: "Digital Marketing", count: "1,200+", icon: "📈" },
                { category: "Content Writing", count: "900+", icon: "✍️" },
                { category: "Mobile Apps", count: "750+", icon: "📱" },
                { category: "Data Science", count: "600+", icon: "📊" }
              ].map((item, index) => (
                <div key={index} className="card p-6 text-center hover:shadow-lg transition-shadow duration-300">
                  <div className="text-4xl mb-3">{item.icon}</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    {item.category}
                  </h3>
                  <p className="text-primary-600 font-bold">{item.count} Success Stories</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA */}
      <div className="py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Write Your Success Story?
            </h2>
            <p className="text-primary-100 mb-8">
              Join thousands of professionals who have transformed their careers with GlobalConnect.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                Get Started Today
              </button>
              <button className="border border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                Share Your Story
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
