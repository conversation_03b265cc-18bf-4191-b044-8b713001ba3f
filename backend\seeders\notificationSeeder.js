import Notification from '../models/notificationModel.js';

// Notification templates for different types
const notificationTemplates = {
  bid: [
    {
      title: 'New Bid Received',
      content: 'You have received a new bid on your project "{projectTitle}"',
      type: 'bid'
    },
    {
      title: 'Bid Accepted',
      content: 'Congratulations! Your bid on "{projectTitle}" has been accepted',
      type: 'bid_update'
    },
    {
      title: 'Bid Rejected',
      content: 'Your bid on "{projectTitle}" was not selected this time',
      type: 'bid_update'
    }
  ],
  project: [
    {
      title: 'Project Status Updated',
      content: 'The status of "{projectTitle}" has been updated to {status}',
      type: 'project_update'
    },
    {
      title: 'Project Deadline Approaching',
      content: 'The deadline for "{projectTitle}" is approaching in 3 days',
      type: 'project_update'
    },
    {
      title: 'Project Completed',
      content: 'The project "{projectTitle}" has been marked as completed',
      type: 'project_update'
    }
  ],
  message: [
    {
      title: 'New Message',
      content: 'You have a new message from {senderName}',
      type: 'message'
    },
    {
      title: 'Project Discussion',
      content: '{senderName} sent a message about "{projectTitle}"',
      type: 'message'
    }
  ],
  document: [
    {
      title: 'Document Uploaded',
      content: 'A new document "{documentTitle}" has been uploaded to "{projectTitle}"',
      type: 'document'
    },
    {
      title: 'Document Updated',
      content: 'The document "{documentTitle}" has been updated',
      type: 'document'
    }
  ],
  system: [
    {
      title: 'Welcome to GlobalConnect',
      content: 'Welcome to GlobalConnect! Complete your profile to get started',
      type: 'system'
    },
    {
      title: 'Profile Verification',
      content: 'Your profile has been verified and is now live',
      type: 'system'
    },
    {
      title: 'Payment Processed',
      content: 'Your payment of ${amount} has been processed successfully',
      type: 'system'
    }
  ]
};

// Seed notifications
export const seedNotifications = async (users, projects, bids) => {
  try {
    const notifications = [];
    
    // Create notifications for each user
    for (const user of users) {
      const userNotifications = [];
      
      // Welcome notification for all users
      const welcomeTemplate = notificationTemplates.system[0];
      userNotifications.push({
        recipient: user._id,
        type: welcomeTemplate.type,
        title: welcomeTemplate.title,
        content: welcomeTemplate.content,
        isRead: Math.random() > 0.7, // 30% chance of being read
        readAt: Math.random() > 0.7 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) : null,
        reference: {
          model: 'User',
          id: user._id
        },
        metadata: {
          userRole: user.role,
          welcomeMessage: true
        },
        createdAt: new Date(user.createdAt || Date.now() - 30 * 24 * 60 * 60 * 1000)
      });
      
      // Role-specific notifications
      if (user.role === 'client') {
        // Notifications for clients about their projects
        const clientProjects = projects.filter(p => p.client.toString() === user._id.toString());
        
        for (const project of clientProjects) {
          // Bid notifications
          const projectBids = bids.filter(b => b.project.toString() === project._id.toString());
          
          for (const bid of projectBids.slice(0, 3)) { // Limit to 3 notifications per project
            const bidTemplate = notificationTemplates.bid[0];
            userNotifications.push({
              recipient: user._id,
              type: bidTemplate.type,
              title: bidTemplate.title,
              content: bidTemplate.content.replace('{projectTitle}', project.title),
              isRead: Math.random() > 0.5,
              readAt: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000) : null,
              reference: {
                model: 'Bid',
                id: bid._id
              },
              metadata: {
                projectId: project._id.toString(),
                projectTitle: project.title,
                bidAmount: bid.amount,
                vendorName: users.find(u => u._id.toString() === bid.vendor.toString())?.name
              },
              createdAt: new Date(bid.createdAt || Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000)
            });
          }
          
          // Project status notifications
          if (project.status !== 'open') {
            const statusTemplate = notificationTemplates.project[0];
            userNotifications.push({
              recipient: user._id,
              type: statusTemplate.type,
              title: statusTemplate.title,
              content: statusTemplate.content
                .replace('{projectTitle}', project.title)
                .replace('{status}', project.status),
              isRead: Math.random() > 0.6,
              readAt: Math.random() > 0.6 ? new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000) : null,
              reference: {
                model: 'Project',
                id: project._id
              },
              metadata: {
                projectId: project._id.toString(),
                projectTitle: project.title,
                oldStatus: 'open',
                newStatus: project.status
              },
              createdAt: new Date(project.updatedAt || Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
            });
          }
        }
      } else if (user.role === 'vendor') {
        // Notifications for vendors about their bids
        const vendorBids = bids.filter(b => b.vendor.toString() === user._id.toString());
        
        for (const bid of vendorBids) {
          const project = projects.find(p => p._id.toString() === bid.project.toString());
          if (!project) continue;
          
          // Bid status notifications
          if (bid.status === 'accepted') {
            const acceptedTemplate = notificationTemplates.bid[1];
            userNotifications.push({
              recipient: user._id,
              type: acceptedTemplate.type,
              title: acceptedTemplate.title,
              content: acceptedTemplate.content.replace('{projectTitle}', project.title),
              isRead: Math.random() > 0.3,
              readAt: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 2 * 24 * 60 * 60 * 1000) : null,
              reference: {
                model: 'Bid',
                id: bid._id
              },
              metadata: {
                projectId: project._id.toString(),
                projectTitle: project.title,
                bidAmount: bid.amount,
                clientName: users.find(u => u._id.toString() === project.client.toString())?.name
              },
              createdAt: new Date(bid.updatedAt || Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000)
            });
          } else if (bid.status === 'rejected') {
            const rejectedTemplate = notificationTemplates.bid[2];
            userNotifications.push({
              recipient: user._id,
              type: rejectedTemplate.type,
              title: rejectedTemplate.title,
              content: rejectedTemplate.content.replace('{projectTitle}', project.title),
              isRead: Math.random() > 0.4,
              readAt: Math.random() > 0.4 ? new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000) : null,
              reference: {
                model: 'Bid',
                id: bid._id
              },
              metadata: {
                projectId: project._id.toString(),
                projectTitle: project.title,
                bidAmount: bid.amount
              },
              createdAt: new Date(bid.updatedAt || Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000)
            });
          }
        }
      }
      
      // Message notifications for all users
      const messageTemplate = notificationTemplates.message[0];
      const numMessageNotifications = Math.floor(Math.random() * 3) + 1; // 1-3 message notifications
      
      for (let i = 0; i < numMessageNotifications; i++) {
        const randomSender = users[Math.floor(Math.random() * users.length)];
        if (randomSender._id.toString() === user._id.toString()) continue;
        
        userNotifications.push({
          recipient: user._id,
          type: messageTemplate.type,
          title: messageTemplate.title,
          content: messageTemplate.content.replace('{senderName}', randomSender.name),
          isRead: Math.random() > 0.4,
          readAt: Math.random() > 0.4 ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) : null,
          reference: {
            model: 'User',
            id: randomSender._id
          },
          metadata: {
            senderId: randomSender._id.toString(),
            senderName: randomSender.name,
            messagePreview: 'Hi! I wanted to discuss...'
          },
          createdAt: new Date(Date.now() - Math.random() * 2 * 24 * 60 * 60 * 1000)
        });
      }
      
      notifications.push(...userNotifications);
    }
    
    // Sort notifications by creation date (newest first)
    notifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    const createdNotifications = await Notification.insertMany(notifications);
    console.log(`✅ Created ${createdNotifications.length} notifications`);
    return createdNotifications;
  } catch (error) {
    console.error('❌ Error seeding notifications:', error);
    return [];
  }
};
