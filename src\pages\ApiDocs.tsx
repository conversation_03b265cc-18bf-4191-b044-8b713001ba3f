import React, { useState } from 'react';
import { Code, Book, Key, Zap, Copy, ExternalLink } from 'lucide-react';

export default function ApiDocs() {
  const [activeTab, setActiveTab] = useState('overview');

  const codeExample = `// Initialize the GlobalConnect API client
const client = new GlobalConnectAPI({
  apiKey: 'your-api-key',
  baseURL: 'https://api.globalconnect.com/v1'
});

// Create a new project
const project = await client.projects.create({
  title: 'Website Development',
  description: 'Build a modern e-commerce website',
  budget: 5000,
  category: 'web-development',
  deadline: '2024-12-31'
});

// Get project bids
const bids = await client.projects.getBids(project.id);

// Accept a bid
await client.bids.accept(bidId);`;

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <Code className="w-16 h-16 mx-auto mb-6 text-blue-200" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              API Documentation
            </h1>
            <p className="text-xl text-blue-100 leading-relaxed">
              Integrate GlobalConnect into your applications with our powerful REST API. Build custom solutions and automate your workflows.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-secondary-200 sticky top-0 z-10">
        <div className="container-custom">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'authentication', label: 'Authentication' },
              { id: 'endpoints', label: 'Endpoints' },
              { id: 'examples', label: 'Examples' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-6xl mx-auto">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-12">
                <div>
                  <h2 className="text-3xl font-bold text-secondary-900 mb-6">
                    Getting Started
                  </h2>
                  <p className="text-lg text-secondary-600 mb-8">
                    The GlobalConnect API allows you to programmatically access and manage projects, bids, users, and more. Our RESTful API uses JSON for data exchange and standard HTTP response codes.
                  </p>
                  
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="card p-6">
                      <Book className="w-8 h-8 text-blue-600 mb-4" />
                      <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                        RESTful Design
                      </h3>
                      <p className="text-secondary-600">
                        Clean, predictable URLs and standard HTTP methods make our API easy to understand and use.
                      </p>
                    </div>
                    
                    <div className="card p-6">
                      <Key className="w-8 h-8 text-green-600 mb-4" />
                      <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                        Secure Authentication
                      </h3>
                      <p className="text-secondary-600">
                        API key-based authentication with optional OAuth 2.0 support for enhanced security.
                      </p>
                    </div>
                    
                    <div className="card p-6">
                      <Zap className="w-8 h-8 text-purple-600 mb-4" />
                      <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                        Real-time Updates
                      </h3>
                      <p className="text-secondary-600">
                        WebSocket support for real-time notifications and live data updates.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-secondary-900 mb-4">
                    Base URL
                  </h3>
                  <div className="bg-secondary-900 text-white p-4 rounded-lg font-mono">
                    https://api.globalconnect.com/v1
                  </div>
                </div>
              </div>
            )}

            {/* Authentication Tab */}
            {activeTab === 'authentication' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-secondary-900 mb-6">
                    Authentication
                  </h2>
                  <p className="text-lg text-secondary-600 mb-8">
                    All API requests require authentication using an API key. Include your API key in the Authorization header of each request.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                    API Key Authentication
                  </h3>
                  <div className="bg-secondary-900 text-white p-4 rounded-lg font-mono mb-4">
                    Authorization: Bearer YOUR_API_KEY
                  </div>
                  <p className="text-secondary-600">
                    You can generate API keys from your account dashboard. Keep your API keys secure and never expose them in client-side code.
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <h4 className="font-semibold text-yellow-800 mb-2">Security Best Practices</h4>
                  <ul className="text-yellow-700 space-y-1">
                    <li>• Store API keys securely in environment variables</li>
                    <li>• Use different API keys for different environments</li>
                    <li>• Rotate API keys regularly</li>
                    <li>• Monitor API key usage for suspicious activity</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Endpoints Tab */}
            {activeTab === 'endpoints' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-secondary-900 mb-6">
                    API Endpoints
                  </h2>
                </div>

                <div className="space-y-6">
                  {[
                    {
                      method: 'GET',
                      endpoint: '/projects',
                      description: 'List all projects',
                      color: 'green'
                    },
                    {
                      method: 'POST',
                      endpoint: '/projects',
                      description: 'Create a new project',
                      color: 'blue'
                    },
                    {
                      method: 'GET',
                      endpoint: '/projects/{id}',
                      description: 'Get project details',
                      color: 'green'
                    },
                    {
                      method: 'PUT',
                      endpoint: '/projects/{id}',
                      description: 'Update a project',
                      color: 'yellow'
                    },
                    {
                      method: 'DELETE',
                      endpoint: '/projects/{id}',
                      description: 'Delete a project',
                      color: 'red'
                    },
                    {
                      method: 'GET',
                      endpoint: '/projects/{id}/bids',
                      description: 'Get project bids',
                      color: 'green'
                    },
                    {
                      method: 'POST',
                      endpoint: '/bids',
                      description: 'Submit a bid',
                      color: 'blue'
                    },
                    {
                      method: 'PUT',
                      endpoint: '/bids/{id}/accept',
                      description: 'Accept a bid',
                      color: 'yellow'
                    }
                  ].map((endpoint, index) => (
                    <div key={index} className="card p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                            endpoint.color === 'green' ? 'bg-green-100 text-green-800' :
                            endpoint.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                            endpoint.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {endpoint.method}
                          </span>
                          <code className="font-mono text-secondary-900">{endpoint.endpoint}</code>
                        </div>
                        <p className="text-secondary-600">{endpoint.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Examples Tab */}
            {activeTab === 'examples' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-secondary-900 mb-6">
                    Code Examples
                  </h2>
                  <p className="text-lg text-secondary-600 mb-8">
                    Here are some common use cases and code examples to help you get started with the GlobalConnect API.
                  </p>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-secondary-900">
                      JavaScript/Node.js Example
                    </h3>
                    <button className="flex items-center space-x-2 text-primary-600 hover:text-primary-700">
                      <Copy className="w-4 h-4" />
                      <span>Copy</span>
                    </button>
                  </div>
                  <div className="bg-secondary-900 text-white p-6 rounded-lg overflow-x-auto">
                    <pre className="text-sm">
                      <code>{codeExample}</code>
                    </pre>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="card p-6">
                    <h4 className="font-semibold text-secondary-900 mb-3">SDKs & Libraries</h4>
                    <ul className="space-y-2 text-secondary-600">
                      <li>• JavaScript/TypeScript SDK</li>
                      <li>• Python SDK</li>
                      <li>• PHP SDK</li>
                      <li>• Ruby SDK</li>
                    </ul>
                  </div>
                  
                  <div className="card p-6">
                    <h4 className="font-semibold text-secondary-900 mb-3">Resources</h4>
                    <ul className="space-y-2">
                      <li>
                        <a href="#" className="text-primary-600 hover:text-primary-700 flex items-center">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Postman Collection
                        </a>
                      </li>
                      <li>
                        <a href="#" className="text-primary-600 hover:text-primary-700 flex items-center">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          OpenAPI Specification
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
