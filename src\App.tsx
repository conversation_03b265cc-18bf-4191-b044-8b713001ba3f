import React, { useState, useEffect } from 'react';
import AuthModal from './components/Auth/AuthModal';
import AppRouter from './components/Router/AppRouter';
import WelcomePopup, { useWelcomePopup } from './components/WelcomePopup';
import { useNotifications } from './components/UI/Notifications';
import { authService, User } from './services/authService';

function App() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentView, setCurrentView] = useState<'landing' | 'dashboard'>('landing');
  const [isLoading, setIsLoading] = useState(true);

  const { notifications, removeNotification, showSuccess, showError } = useNotifications();
  const { isOpen: isWelcomeOpen, closePopup: closeWelcome, openPopup: openWelcome } = useWelcomePopup();

  // Check authentication status on app load
  useEffect(() => {
    const checkAuth = async () => {
      const user = authService.getCurrentUser();
      const token = authService.getToken();

      if (user && token) {
        try {
          const isValid = await authService.verifyToken();
          if (isValid) {
            setIsAuthenticated(true);
            setCurrentUser(user);
            setCurrentView('dashboard');
          } else {
            authService.logout();
          }
        } catch (error) {
          authService.logout();
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Listen for authentication events
  useEffect(() => {
    const handleAuthLogout = () => {
      setIsAuthenticated(false);
      setCurrentUser(null);
      setCurrentView('landing');
      showError('Session Expired', 'Please log in again');
    };

    const handleAuthUnauthorized = () => {
      setIsAuthenticated(false);
      setCurrentUser(null);
      setCurrentView('landing');
      showError('Authentication Required', 'Please log in to continue');
    };

    window.addEventListener('auth:logout', handleAuthLogout);
    window.addEventListener('auth:unauthorized', handleAuthUnauthorized);

    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout);
      window.removeEventListener('auth:unauthorized', handleAuthUnauthorized);
    };
  }, [showError]);

  const handleAuthSuccess = (userData: any) => {
    setIsAuthenticated(true);
    setCurrentUser(userData);
    setCurrentView('dashboard');
    setIsAuthModalOpen(false);
    showSuccess('Welcome!', `Successfully signed ${authMode === 'signin' ? 'in' : 'up'}`);
  };

  const handleUserUpdate = (updatedUser: User) => {
    setCurrentUser(updatedUser);
    showSuccess('Profile Updated', 'Your profile has been successfully updated');
  };

  const handleSignOut = () => {
    authService.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setCurrentView('landing');
    showSuccess('Signed out', 'You have been successfully signed out');
  };

  const openAuthModal = (mode: 'signin' | 'signup') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const handleTryDemo = () => {
    // Scroll to demo guide section
    const demoSection = document.getElementById('demo-guide');
    if (demoSection) {
      demoSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-secondary-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <AppRouter
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        notifications={notifications}
        onSignIn={() => openAuthModal('signin')}
        onSignUp={() => openAuthModal('signup')}
        onSignOut={handleSignOut}
        onGetStarted={() => openAuthModal('signup')}
        onTryDemo={openWelcome}
        onUserUpdate={handleUserUpdate}
        removeNotification={removeNotification}
      />

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode={authMode}
        onAuthSuccess={handleAuthSuccess}
      />

      <WelcomePopup
        isOpen={isWelcomeOpen && !isAuthenticated}
        onClose={closeWelcome}
        onTryDemo={handleTryDemo}
      />
    </>
  );
}

export default App;