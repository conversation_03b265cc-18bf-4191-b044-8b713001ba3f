import axios from 'axios';

const API_URL = 'http://localhost:5001/api/bids';

export interface Bid {
  _id: string;
  project: string | {
    _id: string;
    title: string;
    description: string;
    budget: number;
    deadline: string;
    status: string;
    category: string;
    client: {
      _id: string;
      name: string;
      email: string;
      company?: string;
      companyLogo?: string;
    };
  };
  vendor: {
    _id: string;
    name: string;
    email: string;
    company?: string;
    companyLogo?: string;
    rating?: number;
  };
  amount: number;
  proposal: string;
  deliveryTime: number;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  isCounterOffer: boolean;
  originalBid?: string;
  revisions: number;
  milestones: Array<{
    title: string;
    description: string;
    amount: number;
    deadline: string;
  }>;
  attachments: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  competitiveAnalysis?: {
    position: string;
    percentile: number;
    averageAmount: number;
    lowestAmount: number;
    highestAmount: number;
  };
  optimizationScore?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBidData {
  project: string;
  amount: number;
  proposal: string;
  deliveryTime: number;
  milestones?: Array<{
    title: string;
    description: string;
    amount: number;
    deadline: string;
  }>;
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export interface UpdateBidData {
  amount?: number;
  proposal?: string;
  deliveryTime?: number;
  milestones?: Array<{
    title: string;
    description: string;
    amount: number;
    deadline: string;
  }>;
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

class BidService {
  async createBid(data: CreateBidData): Promise<Bid> {
    try {
      const response = await axios.post(API_URL, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create bid');
    }
  }

  async getProjectBids(projectId: string): Promise<Bid[]> {
    try {
      const response = await axios.get(`${API_URL}/project/${projectId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch bids');
    }
  }

  async getBidById(id: string): Promise<Bid> {
    try {
      const response = await axios.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch bid');
    }
  }

  async updateBid(id: string, data: UpdateBidData): Promise<Bid> {
    try {
      const response = await axios.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update bid');
    }
  }

  async updateBidStatus(id: string, status: 'accepted' | 'rejected'): Promise<Bid> {
    try {
      const response = await axios.put(`${API_URL}/${id}/status`, { status });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update bid status');
    }
  }

  async withdrawBid(id: string): Promise<void> {
    try {
      await axios.put(`${API_URL}/${id}/withdraw`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to withdraw bid');
    }
  }

  async createCounterOffer(id: string, data: Partial<CreateBidData>): Promise<Bid> {
    try {
      const response = await axios.post(`${API_URL}/${id}/counter`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create counter offer');
    }
  }

  // Helper methods
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  formatDeliveryTime(days: number): string {
    if (days === 1) {
      return '1 day';
    } else if (days < 7) {
      return `${days} days`;
    } else if (days === 7) {
      return '1 week';
    } else if (days < 30) {
      const weeks = Math.floor(days / 7);
      const remainingDays = days % 7;
      if (remainingDays === 0) {
        return `${weeks} week${weeks > 1 ? 's' : ''}`;
      } else {
        return `${weeks} week${weeks > 1 ? 's' : ''} ${remainingDays} day${remainingDays > 1 ? 's' : ''}`;
      }
    } else {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays === 0) {
        return `${months} month${months > 1 ? 's' : ''}`;
      } else {
        return `${months} month${months > 1 ? 's' : ''} ${remainingDays} day${remainingDays > 1 ? 's' : ''}`;
      }
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending':
        return 'bg-warning-100 text-warning-700';
      case 'accepted':
        return 'bg-success-100 text-success-700';
      case 'rejected':
        return 'bg-error-100 text-error-700';
      case 'withdrawn':
        return 'bg-secondary-100 text-secondary-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'accepted':
        return '✅';
      case 'rejected':
        return '❌';
      case 'withdrawn':
        return '🚫';
      default:
        return '❓';
    }
  }

  calculateBidScore(bid: Bid, projectBudget: number): number {
    // Simple scoring algorithm based on multiple factors
    let score = 0;

    // Price competitiveness (40% weight)
    const priceRatio = bid.amount / projectBudget;
    if (priceRatio <= 0.8) {
      score += 40; // Very competitive
    } else if (priceRatio <= 1.0) {
      score += 30; // Competitive
    } else if (priceRatio <= 1.2) {
      score += 20; // Reasonable
    } else {
      score += 10; // Expensive
    }

    // Vendor rating (30% weight)
    if (bid.vendor.rating) {
      score += (bid.vendor.rating / 5) * 30;
    } else {
      score += 15; // Default for new vendors
    }

    // Delivery time (20% weight)
    if (bid.deliveryTime <= 7) {
      score += 20; // Very fast
    } else if (bid.deliveryTime <= 14) {
      score += 15; // Fast
    } else if (bid.deliveryTime <= 30) {
      score += 10; // Reasonable
    } else {
      score += 5; // Slow
    }

    // Proposal quality (10% weight) - based on length and detail
    const proposalLength = bid.proposal.length;
    if (proposalLength >= 500) {
      score += 10; // Detailed
    } else if (proposalLength >= 200) {
      score += 7; // Good
    } else {
      score += 3; // Basic
    }

    return Math.round(score);
  }

  getBidRecommendation(score: number): {
    level: 'excellent' | 'good' | 'fair' | 'poor';
    color: string;
    message: string;
  } {
    if (score >= 80) {
      return {
        level: 'excellent',
        color: 'text-success-600',
        message: 'Highly recommended bid with excellent value'
      };
    } else if (score >= 65) {
      return {
        level: 'good',
        color: 'text-primary-600',
        message: 'Good bid with competitive terms'
      };
    } else if (score >= 50) {
      return {
        level: 'fair',
        color: 'text-warning-600',
        message: 'Fair bid, consider other options'
      };
    } else {
      return {
        level: 'poor',
        color: 'text-error-600',
        message: 'Below average bid, proceed with caution'
      };
    }
  }
}

export const bidService = new BidService();
