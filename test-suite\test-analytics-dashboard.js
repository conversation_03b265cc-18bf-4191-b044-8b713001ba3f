#!/usr/bin/env node

/**
 * Analytics Dashboard Feature Tests
 * Tests all analytics and dashboard functionality
 */

const { TestRunner, DEMO_USERS, BASE_URL } = require('./comprehensive-feature-test');

class AnalyticsDashboardTests {
  constructor() {
    this.runner = new TestRunner();
  }

  async runAllTests() {
    console.log('\n📊 Starting Analytics Dashboard Tests...\n');

    try {
      // Authenticate users
      await this.runner.authenticate('clients', 0); // <PERSON>
      await this.runner.authenticate('clients', 1); // <PERSON>
      await this.runner.authenticate('vendors', 0); // <PERSON>
      await this.runner.authenticate('vendors', 1); // <PERSON>
      await this.runner.authenticate('admin');

      // Run all analytics dashboard tests
      await this.testDashboardStatistics();
      await this.testProjectAnalytics();
      await this.testRevenueTracking();
      await this.testUserEngagement();
      await this.testPerformanceMetrics();
      await this.testAnalyticsFiltering();
      await this.testDataExport();
      await this.testRealTimeUpdates();

    } catch (error) {
      console.error('Analytics dashboard test suite failed:', error);
    }

    return this.runner.generateReport();
  }

  async testDashboardStatistics() {
    this.runner.startTest('Dashboard Statistics', 'Test personalized dashboard statistics for different user types');

    try {
      // Test client dashboard
      const clientDashboard = await this.runner.makeRequest('GET', '/analytics/dashboard', null, 'clients', 0);
      
      if (clientDashboard && typeof clientDashboard === 'object') {
        this.runner.log('Client dashboard data retrieved successfully');
        
        // Check for expected client metrics
        const expectedClientFields = ['totalProjects', 'activeProjects', 'totalSpent', 'averageProjectCost'];
        const availableFields = Object.keys(clientDashboard);
        
        this.runner.log(`Client dashboard fields: ${availableFields.join(', ')}`);
        
        const missingFields = expectedClientFields.filter(field => !availableFields.includes(field));
        if (missingFields.length > 0) {
          this.runner.log(`Missing client dashboard fields: ${missingFields.join(', ')}`);
        }
      } else {
        throw new Error('Client dashboard data not available');
      }

      // Test vendor dashboard
      const vendorDashboard = await this.runner.makeRequest('GET', '/analytics/dashboard', null, 'vendors', 0);
      
      if (vendorDashboard && typeof vendorDashboard === 'object') {
        this.runner.log('Vendor dashboard data retrieved successfully');
        
        // Check for expected vendor metrics
        const expectedVendorFields = ['totalBids', 'acceptedBids', 'totalEarnings', 'successRate'];
        const vendorFields = Object.keys(vendorDashboard);
        
        this.runner.log(`Vendor dashboard fields: ${vendorFields.join(', ')}`);
        
        const missingVendorFields = expectedVendorFields.filter(field => !vendorFields.includes(field));
        if (missingVendorFields.length > 0) {
          this.runner.log(`Missing vendor dashboard fields: ${missingVendorFields.join(', ')}`);
        }
      } else {
        this.runner.log('Vendor dashboard data not available');
      }

      // Test admin dashboard
      const adminDashboard = await this.runner.makeRequest('GET', '/analytics/dashboard', null, 'admin');
      
      if (adminDashboard && typeof adminDashboard === 'object') {
        this.runner.log('Admin dashboard data retrieved successfully');
        
        // Check for expected admin metrics
        const expectedAdminFields = ['totalUsers', 'totalProjects', 'totalRevenue', 'platformGrowth'];
        const adminFields = Object.keys(adminDashboard);
        
        this.runner.log(`Admin dashboard fields: ${adminFields.join(', ')}`);
        
        const missingAdminFields = expectedAdminFields.filter(field => !adminFields.includes(field));
        if (missingAdminFields.length > 0) {
          this.runner.log(`Missing admin dashboard fields: ${missingAdminFields.join(', ')}`);
        }
      } else {
        this.runner.log('Admin dashboard data not available');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testProjectAnalytics() {
    this.runner.startTest('Project Analytics', 'Test project performance analytics and insights');

    try {
      // Test overall project analytics
      const projectAnalytics = await this.runner.makeRequest('GET', '/analytics/projects', null, 'clients', 0);
      
      if (projectAnalytics && typeof projectAnalytics === 'object') {
        this.runner.log('Project analytics data retrieved successfully');
        
        const analyticsFields = Object.keys(projectAnalytics);
        this.runner.log(`Project analytics fields: ${analyticsFields.join(', ')}`);
      }

      // Test individual project analytics
      const projects = await this.runner.makeRequest('GET', '/projects', null, 'clients', 0);
      
      if (projects.length > 0) {
        const projectId = projects[0]._id;
        
        try {
          const individualProjectAnalytics = await this.runner.makeRequest('GET', `/analytics/projects/${projectId}`, null, 'clients', 0);
          
          if (individualProjectAnalytics) {
            this.runner.log(`Individual project analytics retrieved for project ${projectId}`);
            
            // Check for expected project metrics
            const expectedProjectMetrics = ['bidCount', 'averageBidAmount', 'timeToCompletion', 'clientSatisfaction'];
            const projectMetrics = Object.keys(individualProjectAnalytics);
            
            this.runner.log(`Project metrics available: ${projectMetrics.join(', ')}`);
          }
        } catch (individualError) {
          this.runner.log('Individual project analytics endpoint not implemented yet');
        }
      }

      // Test project performance trends
      try {
        const performanceTrends = await this.runner.makeRequest('GET', '/analytics/projects/trends', null, 'clients', 0);
        
        if (performanceTrends && Array.isArray(performanceTrends)) {
          this.runner.log(`Found ${performanceTrends.length} performance trend data points`);
        }
      } catch (trendsError) {
        this.runner.log('Project trends analytics endpoint not implemented yet');
      }

      // Test project category analytics
      try {
        const categoryAnalytics = await this.runner.makeRequest('GET', '/analytics/projects/categories', null, 'clients', 0);
        
        if (categoryAnalytics && typeof categoryAnalytics === 'object') {
          this.runner.log('Project category analytics retrieved successfully');
          
          const categories = Object.keys(categoryAnalytics);
          this.runner.log(`Categories analyzed: ${categories.join(', ')}`);
        }
      } catch (categoryError) {
        this.runner.log('Project category analytics endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testRevenueTracking() {
    this.runner.startTest('Revenue Tracking', 'Test revenue and earnings tracking functionality');

    try {
      // Test client revenue analytics
      const clientRevenue = await this.runner.makeRequest('GET', '/analytics/revenue', null, 'clients', 0);
      
      if (clientRevenue && typeof clientRevenue === 'object') {
        this.runner.log('Client revenue data retrieved successfully');
        
        const revenueFields = Object.keys(clientRevenue);
        this.runner.log(`Client revenue fields: ${revenueFields.join(', ')}`);
        
        // Check for expected revenue metrics
        const expectedRevenueFields = ['totalSpent', 'monthlySpending', 'averageProjectCost', 'costSavings'];
        const missingRevenueFields = expectedRevenueFields.filter(field => !revenueFields.includes(field));
        
        if (missingRevenueFields.length > 0) {
          this.runner.log(`Missing revenue fields: ${missingRevenueFields.join(', ')}`);
        }
      }

      // Test vendor earnings analytics
      const vendorEarnings = await this.runner.makeRequest('GET', '/analytics/earnings', null, 'vendors', 0);
      
      if (vendorEarnings && typeof vendorEarnings === 'object') {
        this.runner.log('Vendor earnings data retrieved successfully');
        
        const earningsFields = Object.keys(vendorEarnings);
        this.runner.log(`Vendor earnings fields: ${earningsFields.join(', ')}`);
        
        // Check for expected earnings metrics
        const expectedEarningsFields = ['totalEarnings', 'monthlyEarnings', 'averageProjectValue', 'pendingPayments'];
        const missingEarningsFields = expectedEarningsFields.filter(field => !earningsFields.includes(field));
        
        if (missingEarningsFields.length > 0) {
          this.runner.log(`Missing earnings fields: ${missingEarningsFields.join(', ')}`);
        }
      }

      // Test revenue trends over time
      try {
        const revenueTrends = await this.runner.makeRequest('GET', '/analytics/revenue/trends?period=6months', null, 'clients', 0);
        
        if (revenueTrends && Array.isArray(revenueTrends)) {
          this.runner.log(`Found ${revenueTrends.length} revenue trend data points`);
        }
      } catch (trendsError) {
        this.runner.log('Revenue trends endpoint not implemented yet');
      }

      // Test earnings breakdown by project category
      try {
        const earningsBreakdown = await this.runner.makeRequest('GET', '/analytics/earnings/breakdown', null, 'vendors', 0);
        
        if (earningsBreakdown && typeof earningsBreakdown === 'object') {
          this.runner.log('Earnings breakdown by category retrieved successfully');
        }
      } catch (breakdownError) {
        this.runner.log('Earnings breakdown endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testUserEngagement() {
    this.runner.startTest('User Engagement', 'Test user activity and engagement metrics');

    try {
      // Test user activity analytics
      const userActivity = await this.runner.makeRequest('GET', '/analytics/user/activity', null, 'clients', 0);
      
      if (userActivity && typeof userActivity === 'object') {
        this.runner.log('User activity data retrieved successfully');
        
        const activityFields = Object.keys(userActivity);
        this.runner.log(`User activity fields: ${activityFields.join(', ')}`);
        
        // Check for expected activity metrics
        const expectedActivityFields = ['loginFrequency', 'sessionDuration', 'pageViews', 'actionsPerSession'];
        const missingActivityFields = expectedActivityFields.filter(field => !activityFields.includes(field));
        
        if (missingActivityFields.length > 0) {
          this.runner.log(`Missing activity fields: ${missingActivityFields.join(', ')}`);
        }
      }

      // Test engagement metrics for vendors
      const vendorEngagement = await this.runner.makeRequest('GET', '/analytics/user/engagement', null, 'vendors', 0);
      
      if (vendorEngagement && typeof vendorEngagement === 'object') {
        this.runner.log('Vendor engagement data retrieved successfully');
        
        const engagementFields = Object.keys(vendorEngagement);
        this.runner.log(`Vendor engagement fields: ${engagementFields.join(', ')}`);
      }

      // Test platform-wide engagement (admin view)
      try {
        const platformEngagement = await this.runner.makeRequest('GET', '/analytics/platform/engagement', null, 'admin');
        
        if (platformEngagement && typeof platformEngagement === 'object') {
          this.runner.log('Platform engagement data retrieved successfully');
          
          const platformFields = Object.keys(platformEngagement);
          this.runner.log(`Platform engagement fields: ${platformFields.join(', ')}`);
        }
      } catch (platformError) {
        this.runner.log('Platform engagement analytics endpoint not implemented yet');
      }

      // Test user behavior analytics
      try {
        const userBehavior = await this.runner.makeRequest('GET', '/analytics/user/behavior', null, 'clients', 0);
        
        if (userBehavior && typeof userBehavior === 'object') {
          this.runner.log('User behavior analytics retrieved successfully');
        }
      } catch (behaviorError) {
        this.runner.log('User behavior analytics endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testPerformanceMetrics() {
    this.runner.startTest('Performance Metrics', 'Test system and user performance metrics');

    try {
      // Test system performance metrics (admin only)
      try {
        const systemMetrics = await this.runner.makeRequest('GET', '/analytics/system/performance', null, 'admin');
        
        if (systemMetrics && typeof systemMetrics === 'object') {
          this.runner.log('System performance metrics retrieved successfully');
          
          const systemFields = Object.keys(systemMetrics);
          this.runner.log(`System metrics: ${systemFields.join(', ')}`);
          
          // Check for expected system metrics
          const expectedSystemFields = ['responseTime', 'uptime', 'errorRate', 'throughput'];
          const missingSystemFields = expectedSystemFields.filter(field => !systemFields.includes(field));
          
          if (missingSystemFields.length > 0) {
            this.runner.log(`Missing system metrics: ${missingSystemFields.join(', ')}`);
          }
        }
      } catch (systemError) {
        this.runner.log('System performance metrics endpoint not implemented yet');
      }

      // Test user performance metrics
      const userPerformance = await this.runner.makeRequest('GET', '/analytics/user/performance', null, 'vendors', 0);
      
      if (userPerformance && typeof userPerformance === 'object') {
        this.runner.log('User performance metrics retrieved successfully');
        
        const performanceFields = Object.keys(userPerformance);
        this.runner.log(`User performance fields: ${performanceFields.join(', ')}`);
        
        // Check for expected performance metrics
        const expectedPerformanceFields = ['completionRate', 'averageDeliveryTime', 'clientRating', 'repeatClientRate'];
        const missingPerformanceFields = expectedPerformanceFields.filter(field => !performanceFields.includes(field));
        
        if (missingPerformanceFields.length > 0) {
          this.runner.log(`Missing performance fields: ${missingPerformanceFields.join(', ')}`);
        }
      }

      // Test project completion metrics
      try {
        const completionMetrics = await this.runner.makeRequest('GET', '/analytics/projects/completion', null, 'clients', 0);
        
        if (completionMetrics && typeof completionMetrics === 'object') {
          this.runner.log('Project completion metrics retrieved successfully');
        }
      } catch (completionError) {
        this.runner.log('Project completion metrics endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testAnalyticsFiltering() {
    this.runner.startTest('Analytics Filtering', 'Test filtering and date range functionality in analytics');

    try {
      // Test date range filtering
      const dateRanges = [
        { period: '7d', name: 'Last 7 days' },
        { period: '30d', name: 'Last 30 days' },
        { period: '3m', name: 'Last 3 months' },
        { period: '1y', name: 'Last year' }
      ];

      for (const range of dateRanges) {
        try {
          const filteredAnalytics = await this.runner.makeRequest('GET', `/analytics/dashboard?period=${range.period}`, null, 'clients', 0);
          
          if (filteredAnalytics && typeof filteredAnalytics === 'object') {
            this.runner.log(`Analytics for ${range.name} retrieved successfully`);
          }
        } catch (rangeError) {
          this.runner.log(`Date range filtering for ${range.name} not implemented or failed`);
        }
      }

      // Test custom date range
      try {
        const startDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(); // 60 days ago
        const endDate = new Date().toISOString();
        
        const customRangeAnalytics = await this.runner.makeRequest('GET', `/analytics/dashboard?startDate=${startDate}&endDate=${endDate}`, null, 'clients', 0);
        
        if (customRangeAnalytics && typeof customRangeAnalytics === 'object') {
          this.runner.log('Custom date range analytics retrieved successfully');
        }
      } catch (customRangeError) {
        this.runner.log('Custom date range filtering not implemented yet');
      }

      // Test category filtering
      try {
        const categoryAnalytics = await this.runner.makeRequest('GET', '/analytics/projects?category=web-development', null, 'clients', 0);
        
        if (categoryAnalytics && typeof categoryAnalytics === 'object') {
          this.runner.log('Category-filtered analytics retrieved successfully');
        }
      } catch (categoryError) {
        this.runner.log('Category filtering not implemented yet');
      }

      // Test status filtering
      try {
        const statusAnalytics = await this.runner.makeRequest('GET', '/analytics/projects?status=completed', null, 'clients', 0);
        
        if (statusAnalytics && typeof statusAnalytics === 'object') {
          this.runner.log('Status-filtered analytics retrieved successfully');
        }
      } catch (statusError) {
        this.runner.log('Status filtering not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testDataExport() {
    this.runner.startTest('Data Export', 'Test analytics data export functionality');

    try {
      // Test CSV export
      try {
        const csvExport = await this.runner.makeRequest('GET', '/analytics/export?format=csv&type=projects', null, 'clients', 0);
        
        if (csvExport) {
          this.runner.log('CSV export functionality working');
        }
      } catch (csvError) {
        this.runner.log('CSV export not implemented yet');
      }

      // Test JSON export
      try {
        const jsonExport = await this.runner.makeRequest('GET', '/analytics/export?format=json&type=revenue', null, 'clients', 0);
        
        if (jsonExport) {
          this.runner.log('JSON export functionality working');
        }
      } catch (jsonError) {
        this.runner.log('JSON export not implemented yet');
      }

      // Test PDF report generation
      try {
        const pdfReport = await this.runner.makeRequest('POST', '/analytics/reports/generate', { 
          type: 'monthly',
          format: 'pdf',
          includeCharts: true
        }, 'clients', 0);
        
        if (pdfReport && pdfReport.reportId) {
          this.runner.log(`PDF report generated with ID: ${pdfReport.reportId}`);
        }
      } catch (pdfError) {
        this.runner.log('PDF report generation not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testRealTimeUpdates() {
    this.runner.startTest('Real-time Updates', 'Test real-time analytics updates and live data');

    try {
      // Test real-time dashboard endpoint
      try {
        const realtimeData = await this.runner.makeRequest('GET', '/analytics/realtime', null, 'clients', 0);
        
        if (realtimeData && typeof realtimeData === 'object') {
          this.runner.log('Real-time analytics data retrieved successfully');
          
          const realtimeFields = Object.keys(realtimeData);
          this.runner.log(`Real-time fields: ${realtimeFields.join(', ')}`);
        }
      } catch (realtimeError) {
        this.runner.log('Real-time analytics endpoint not implemented yet');
      }

      // Test live metrics endpoint
      try {
        const liveMetrics = await this.runner.makeRequest('GET', '/analytics/live/metrics', null, 'admin');
        
        if (liveMetrics && typeof liveMetrics === 'object') {
          this.runner.log('Live metrics data retrieved successfully');
        }
      } catch (liveError) {
        this.runner.log('Live metrics endpoint not implemented yet');
      }

      // Test analytics refresh endpoint
      try {
        const refreshResult = await this.runner.makeRequest('POST', '/analytics/refresh', {}, 'clients', 0);
        
        if (refreshResult) {
          this.runner.log('Analytics refresh triggered successfully');
        }
      } catch (refreshError) {
        this.runner.log('Analytics refresh endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tests = new AnalyticsDashboardTests();
  tests.runAllTests().then(report => {
    console.log('\nAnalytics Dashboard Tests Complete!');
    process.exit(report.summary.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = AnalyticsDashboardTests;
