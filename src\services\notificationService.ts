import axios from 'axios';
import { io, Socket } from 'socket.io-client';

const API_URL = 'http://localhost:5001/api/notifications';

export interface Notification {
  _id: string;
  recipient: string;
  type: 'message' | 'bid' | 'bid_update' | 'project_update' | 'document' | 'system';
  title: string;
  content: string;
  isRead: boolean;
  readAt?: string;
  reference: {
    model: string;
    id: string;
  };
  metadata: any;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationResponse {
  notifications: Notification[];
  unreadCount: number;
  total: number;
  currentPage: number;
  totalPages: number;
}

class NotificationService {
  private socket: Socket | null = null;
  private listeners: Map<string, Function[]> = new Map();

  // Initialize socket connection for real-time notifications
  initializeSocket(userId: string) {
    if (this.socket) {
      this.socket.disconnect();
    }

    this.socket = io('http://localhost:5001', {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    this.socket.on('connect', () => {
      console.log('Connected to notification socket');
      this.socket?.emit('join_user_room', userId);
    });

    this.socket.on('new_notification', (data) => {
      this.emit('new_notification', data);
    });

    this.socket.on('notification_read', (data) => {
      this.emit('notification_read', data);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from notification socket');
    });
  }

  // Event emitter methods
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);
  }

  off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  // Disconnect socket
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.listeners.clear();
  }

  // API methods
  async getNotifications(page: number = 1, limit: number = 20, unreadOnly: boolean = false): Promise<NotificationResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        unreadOnly: unreadOnly.toString()
      });

      const response = await axios.get(`${API_URL}?${params.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch notifications');
    }
  }

  async markAsRead(notificationId: string): Promise<Notification> {
    try {
      const response = await axios.put(`${API_URL}/${notificationId}/read`);
      this.emit('notification_read', { notificationId });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to mark notification as read');
    }
  }

  async markAllAsRead(type?: string): Promise<{ success: boolean; markedCount: number; unreadCount: number }> {
    try {
      const params = type ? `?type=${type}` : '';
      const response = await axios.put(`${API_URL}/read-all${params}`);
      this.emit('notifications_read_all', { type });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to mark notifications as read');
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await axios.delete(`${API_URL}/${notificationId}`);
      this.emit('notification_deleted', { notificationId });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete notification');
    }
  }

  // Utility methods
  getNotificationIcon(type: string): string {
    const icons = {
      message: '💬',
      bid: '💰',
      bid_update: '📊',
      project_update: '📋',
      document: '📄',
      system: '⚙️'
    };
    return icons[type as keyof typeof icons] || '🔔';
  }

  getNotificationColor(type: string): string {
    const colors = {
      message: 'text-blue-600',
      bid: 'text-green-600',
      bid_update: 'text-yellow-600',
      project_update: 'text-purple-600',
      document: 'text-indigo-600',
      system: 'text-gray-600'
    };
    return colors[type as keyof typeof colors] || 'text-gray-600';
  }

  getNotificationBgColor(type: string): string {
    const colors = {
      message: 'bg-blue-50',
      bid: 'bg-green-50',
      bid_update: 'bg-yellow-50',
      project_update: 'bg-purple-50',
      document: 'bg-indigo-50',
      system: 'bg-gray-50'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-50';
  }

  formatNotificationTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else if (diffInMinutes < 10080) { // 7 days
      const days = Math.floor(diffInMinutes / 1440);
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  // Browser notification support
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  showBrowserNotification(notification: Notification) {
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.content,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: notification._id,
        requireInteraction: false,
        silent: false
      });

      browserNotification.onclick = () => {
        window.focus();
        this.handleNotificationClick(notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds
      setTimeout(() => {
        browserNotification.close();
      }, 5000);
    }
  }

  private handleNotificationClick(notification: Notification) {
    // Navigate to relevant page based on notification type
    const { type, reference, metadata } = notification;
    
    switch (type) {
      case 'message':
        if (metadata.conversationId) {
          window.location.href = `/messages?conversation=${metadata.conversationId}`;
        } else {
          window.location.href = '/messages';
        }
        break;
      case 'bid':
      case 'bid_update':
        if (metadata.projectId) {
          window.location.href = `/projects/${metadata.projectId}`;
        } else {
          window.location.href = '/projects';
        }
        break;
      case 'project_update':
        if (reference.id) {
          window.location.href = `/projects/${reference.id}`;
        } else {
          window.location.href = '/projects';
        }
        break;
      case 'document':
        if (metadata.projectId) {
          window.location.href = `/projects/${metadata.projectId}/documents`;
        } else {
          window.location.href = '/projects';
        }
        break;
      default:
        // For system notifications, just mark as read
        this.markAsRead(notification._id);
        break;
    }
  }

  // Group notifications by type or date
  groupNotifications(notifications: Notification[], groupBy: 'type' | 'date' = 'date'): Record<string, Notification[]> {
    return notifications.reduce((groups, notification) => {
      let key: string;
      
      if (groupBy === 'type') {
        key = notification.type;
      } else {
        const date = new Date(notification.createdAt);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
          key = 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
          key = 'Yesterday';
        } else {
          key = date.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
          });
        }
      }

      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(notification);
      return groups;
    }, {} as Record<string, Notification[]>);
  }
}

export const notificationService = new NotificationService();
