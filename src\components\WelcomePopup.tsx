import React, { useState, useEffect } from 'react';
import { 
  X, 
  Play, 
  Rocket, 
  Users, 
  Briefcase, 
  MessageCircle, 
  BarChart3,
  ArrowRight,
  Sparkles,
  CheckCircle
} from 'lucide-react';

interface WelcomePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onTryDemo: () => void;
}

export default function WelcomePopup({ isOpen, onClose, onTryDemo }: WelcomePopupProps) {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      icon: Rocket,
      title: "Welcome to GlobalConnect!",
      subtitle: "The Future of Global Project Collaboration",
      description: "Experience our complete platform with live demo accounts and real project data. No setup required!",
      features: [
        "9 realistic demo accounts across all user types",
        "8+ live projects with active bids and conversations",
        "Real-time messaging and notifications",
        "Complete project lifecycle management"
      ],
      color: "primary"
    },
    {
      icon: Users,
      title: "Multiple User Perspectives",
      subtitle: "See the Platform from Every Angle",
      description: "Login as different user types to experience the full ecosystem - from project creation to completion.",
      features: [
        "3 Client accounts - Post projects and manage teams",
        "5 Vendor accounts - Submit bids and deliver work", 
        "1 Admin account - Full platform management",
        "Switch between accounts to see different views"
      ],
      color: "blue"
    },
    {
      icon: Briefcase,
      title: "Real Project Environment",
      subtitle: "Explore Live Projects and Workflows",
      description: "Interact with realistic projects, bids, messages, and documents in a fully functional environment.",
      features: [
        "E-commerce websites, mobile apps, and more",
        "Active bidding with competitive proposals",
        "Project conversations and file sharing",
        "Analytics and performance tracking"
      ],
      color: "green"
    }
  ];

  const quickStartCredentials = [
    {
      type: "Client",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      password: "password123",
      description: "Create and manage projects",
      avatar: "👩‍💼",
      color: "primary"
    },
    {
      type: "Vendor", 
      name: "Alex Thompson",
      email: "<EMAIL>",
      password: "password123",
      description: "Submit bids and deliver work",
      avatar: "👨‍💻",
      color: "blue"
    },
    {
      type: "Admin",
      name: "Admin User", 
      email: "<EMAIL>",
      password: "admin123",
      description: "Full platform management",
      avatar: "👑",
      color: "purple"
    }
  ];

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleTryDemo = () => {
    onTryDemo();
    onClose();
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  if (!isOpen) return null;

  const currentSlideData = slides[currentSlide];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 animate-fadeIn">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-slideInUp">
        {/* Header */}
        <div className="relative p-6 bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-t-2xl">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
              <Sparkles className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">GlobalConnect Demo</h1>
              <p className="text-primary-100">Experience the full platform in action</p>
            </div>
          </div>

          {/* Slide Indicators */}
          <div className="flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                  index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-40'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {/* Slide Content */}
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <div className={`w-16 h-16 bg-${currentSlideData.color}-100 rounded-xl flex items-center justify-center mr-4`}>
                <currentSlideData.icon className={`w-8 h-8 text-${currentSlideData.color}-600`} />
              </div>
              <div>
                <h2 className="text-3xl font-bold text-secondary-900 mb-2">
                  {currentSlideData.title}
                </h2>
                <p className="text-lg text-secondary-600">
                  {currentSlideData.subtitle}
                </p>
              </div>
            </div>

            <p className="text-secondary-700 mb-6 text-lg leading-relaxed">
              {currentSlideData.description}
            </p>

            <div className="grid md:grid-cols-2 gap-4">
              {currentSlideData.features.map((feature, index) => (
                <div key={index} className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-secondary-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Start Credentials - Show on last slide */}
          {currentSlide === slides.length - 1 && (
            <div className="mb-8">
              <h3 className="text-xl font-bold text-secondary-900 mb-4">
                Quick Start Demo Accounts
              </h3>
              <div className="grid md:grid-cols-3 gap-4">
                {quickStartCredentials.map((cred, index) => (
                  <div key={index} className={`p-4 border-2 border-${cred.color}-200 rounded-lg hover:border-${cred.color}-300 transition-colors duration-200`}>
                    <div className="flex items-center mb-3">
                      <span className="text-2xl mr-3">{cred.avatar}</span>
                      <div>
                        <h4 className="font-semibold text-secondary-900">{cred.type}</h4>
                        <p className="text-sm text-secondary-600">{cred.name}</p>
                      </div>
                    </div>
                    <div className="space-y-1 text-xs">
                      <div>
                        <span className="text-secondary-500">Email: </span>
                        <code className="bg-secondary-100 px-1 rounded">{cred.email}</code>
                      </div>
                      <div>
                        <span className="text-secondary-500">Password: </span>
                        <code className="bg-secondary-100 px-1 rounded">{cred.password}</code>
                      </div>
                    </div>
                    <p className="text-xs text-secondary-500 mt-2 italic">{cred.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Navigation and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              {currentSlide > 0 && (
                <button
                  onClick={prevSlide}
                  className="px-4 py-2 text-secondary-600 hover:text-secondary-900 transition-colors duration-200"
                >
                  Previous
                </button>
              )}
            </div>

            <div className="flex space-x-4">
              {currentSlide < slides.length - 1 ? (
                <button
                  onClick={nextSlide}
                  className="btn-primary inline-flex items-center"
                >
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </button>
              ) : (
                <div className="flex space-x-3">
                  <button
                    onClick={handleTryDemo}
                    className="btn-primary inline-flex items-center text-lg px-6 py-3"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    Try Demo Now
                  </button>
                  <button
                    onClick={onClose}
                    className="btn-secondary px-6 py-3"
                  >
                    Maybe Later
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-8 p-4 bg-secondary-50 rounded-lg">
            <div className="flex items-start">
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <BarChart3 className="w-4 h-4 text-primary-600" />
              </div>
              <div>
                <h4 className="font-semibold text-secondary-900 mb-1">
                  Complete Demo Environment
                </h4>
                <p className="text-sm text-secondary-600">
                  Our demo includes realistic data: 9 user accounts, 8+ projects, 20+ bids, 
                  50+ messages, and 30+ documents. Experience every feature with real interactions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook to manage welcome popup state
export function useWelcomePopup() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Clear localStorage for demo purposes
    localStorage.removeItem('globalconnect-welcome-seen');

    // Show popup after a short delay
    const timer = setTimeout(() => {
      setIsOpen(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const closePopup = () => {
    setIsOpen(false);
    localStorage.setItem('globalconnect-welcome-seen', 'true');
  };

  const openPopup = () => {
    setIsOpen(true);
  };

  const resetWelcome = () => {
    localStorage.removeItem('globalconnect-welcome-seen');
    setIsOpen(true);
  };

  return {
    isOpen,
    closePopup,
    openPopup,
    resetWelcome
  };
}
