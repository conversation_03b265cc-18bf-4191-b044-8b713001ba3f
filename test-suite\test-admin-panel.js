#!/usr/bin/env node

/**
 * Admin Panel Feature Tests
 * Tests all administrative functionality
 */

const { TestRunner, DEMO_USERS, BASE_URL } = require('./comprehensive-feature-test');

class AdminPanelTests {
  constructor() {
    this.runner = new TestRunner();
  }

  async runAllTests() {
    console.log('\n👑 Starting Admin Panel Tests...\n');

    try {
      // Authenticate users
      await this.runner.authenticate('admin');
      await this.runner.authenticate('clients', 0); // For comparison
      await this.runner.authenticate('vendors', 0); // For comparison

      // Run all admin panel tests
      await this.testUserManagement();
      await this.testRoleAssignment();
      await this.testPlatformAnalytics();
      await this.testSystemConfiguration();
      await this.testContentModeration();
      await this.testSecurityManagement();
      await this.testSystemMonitoring();
      await this.testDataManagement();

    } catch (error) {
      console.error('Admin panel test suite failed:', error);
    }

    return this.runner.generateReport();
  }

  async testUserManagement() {
    this.runner.startTest('User Management', 'Test user management and administration features');

    try {
      // Test getting all users (admin only)
      const allUsers = await this.runner.makeRequest('GET', '/admin/users', null, 'admin');
      
      if (!Array.isArray(allUsers)) {
        throw new Error('Users response is not an array');
      }

      this.runner.log(`Found ${allUsers.length} total users in system`);

      // Verify user structure
      if (allUsers.length > 0) {
        const sampleUser = allUsers[0];
        const requiredFields = ['_id', 'name', 'email', 'role', 'createdAt'];
        
        for (const field of requiredFields) {
          if (!(field in sampleUser)) {
            throw new Error(`User missing required field: ${field}`);
          }
        }

        this.runner.log('User structure validation passed');
      }

      // Test user role distribution
      const roleDistribution = {};
      allUsers.forEach(user => {
        roleDistribution[user.role] = (roleDistribution[user.role] || 0) + 1;
      });

      this.runner.log(`User role distribution: ${JSON.stringify(roleDistribution)}`);

      // Test user search functionality
      try {
        const searchResults = await this.runner.makeRequest('GET', '/admin/users?search=alex', null, 'admin');
        
        if (Array.isArray(searchResults)) {
          this.runner.log(`User search returned ${searchResults.length} results`);
        }
      } catch (searchError) {
        this.runner.log('User search functionality not implemented yet');
      }

      // Test user filtering by role
      try {
        const vendorUsers = await this.runner.makeRequest('GET', '/admin/users?role=vendor', null, 'admin');
        
        if (Array.isArray(vendorUsers)) {
          this.runner.log(`Found ${vendorUsers.length} vendor users`);
        }
      } catch (filterError) {
        this.runner.log('User role filtering not implemented yet');
      }

      // Test that non-admin users cannot access user management
      try {
        await this.runner.makeRequest('GET', '/admin/users', null, 'clients', 0);
        throw new Error('Client should not have access to user management');
      } catch (accessError) {
        this.runner.log('User management correctly restricted to admin users');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testRoleAssignment() {
    this.runner.startTest('Role Assignment', 'Test user role management and permissions');

    try {
      // Get a user to test role assignment
      const allUsers = await this.runner.makeRequest('GET', '/admin/users', null, 'admin');
      const testUser = allUsers.find(user => user.role === 'vendor');
      
      if (!testUser) {
        this.runner.log('No vendor user found for role assignment test');
        this.runner.endTest(true);
        return;
      }

      const originalRole = testUser.role;
      this.runner.log(`Testing role assignment for user: ${testUser.name} (current role: ${originalRole})`);

      // Test role update
      try {
        const roleUpdate = {
          role: 'client',
          reason: 'Testing role assignment functionality'
        };

        const updatedUser = await this.runner.makeRequest('PUT', `/admin/users/${testUser._id}/role`, roleUpdate, 'admin');
        
        if (updatedUser.role !== roleUpdate.role) {
          throw new Error('User role was not updated correctly');
        }

        this.runner.log(`User role updated from ${originalRole} to ${updatedUser.role}`);

        // Revert the role change
        const revertUpdate = {
          role: originalRole,
          reason: 'Reverting test role change'
        };

        await this.runner.makeRequest('PUT', `/admin/users/${testUser._id}/role`, revertUpdate, 'admin');
        this.runner.log('Role change reverted successfully');

      } catch (roleError) {
        this.runner.log('Role assignment endpoint not implemented yet');
      }

      // Test role permissions
      try {
        const rolePermissions = await this.runner.makeRequest('GET', '/admin/roles/permissions', null, 'admin');
        
        if (rolePermissions && typeof rolePermissions === 'object') {
          this.runner.log('Role permissions retrieved successfully');
          
          const roles = Object.keys(rolePermissions);
          this.runner.log(`Available roles: ${roles.join(', ')}`);
        }
      } catch (permissionsError) {
        this.runner.log('Role permissions endpoint not implemented yet');
      }

      // Test bulk role assignment
      try {
        const bulkRoleUpdate = {
          userIds: [testUser._id],
          role: 'client',
          reason: 'Bulk role assignment test'
        };

        const bulkResult = await this.runner.makeRequest('PUT', '/admin/users/bulk/role', bulkRoleUpdate, 'admin');
        this.runner.log('Bulk role assignment completed successfully');
      } catch (bulkError) {
        this.runner.log('Bulk role assignment endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testPlatformAnalytics() {
    this.runner.startTest('Platform Analytics', 'Test platform-wide analytics and reporting');

    try {
      // Test platform overview analytics
      const platformOverview = await this.runner.makeRequest('GET', '/admin/analytics/overview', null, 'admin');
      
      if (platformOverview && typeof platformOverview === 'object') {
        this.runner.log('Platform overview analytics retrieved successfully');
        
        const overviewFields = Object.keys(platformOverview);
        this.runner.log(`Platform overview fields: ${overviewFields.join(', ')}`);
        
        // Check for expected platform metrics
        const expectedFields = ['totalUsers', 'totalProjects', 'totalRevenue', 'activeUsers', 'growthRate'];
        const missingFields = expectedFields.filter(field => !overviewFields.includes(field));
        
        if (missingFields.length > 0) {
          this.runner.log(`Missing platform overview fields: ${missingFields.join(', ')}`);
        }
      } else {
        this.runner.log('Platform overview analytics not available');
      }

      // Test user growth analytics
      try {
        const userGrowth = await this.runner.makeRequest('GET', '/admin/analytics/users/growth', null, 'admin');
        
        if (userGrowth && Array.isArray(userGrowth)) {
          this.runner.log(`User growth data contains ${userGrowth.length} data points`);
        }
      } catch (growthError) {
        this.runner.log('User growth analytics endpoint not implemented yet');
      }

      // Test revenue analytics
      try {
        const revenueAnalytics = await this.runner.makeRequest('GET', '/admin/analytics/revenue', null, 'admin');
        
        if (revenueAnalytics && typeof revenueAnalytics === 'object') {
          this.runner.log('Platform revenue analytics retrieved successfully');
        }
      } catch (revenueError) {
        this.runner.log('Platform revenue analytics endpoint not implemented yet');
      }

      // Test project analytics
      try {
        const projectAnalytics = await this.runner.makeRequest('GET', '/admin/analytics/projects', null, 'admin');
        
        if (projectAnalytics && typeof projectAnalytics === 'object') {
          this.runner.log('Platform project analytics retrieved successfully');
        }
      } catch (projectError) {
        this.runner.log('Platform project analytics endpoint not implemented yet');
      }

      // Test that non-admin users cannot access platform analytics
      try {
        await this.runner.makeRequest('GET', '/admin/analytics/overview', null, 'clients', 0);
        throw new Error('Client should not have access to platform analytics');
      } catch (accessError) {
        this.runner.log('Platform analytics correctly restricted to admin users');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testSystemConfiguration() {
    this.runner.startTest('System Configuration', 'Test system settings and configuration management');

    try {
      // Test getting system configuration
      try {
        const systemConfig = await this.runner.makeRequest('GET', '/admin/config', null, 'admin');
        
        if (systemConfig && typeof systemConfig === 'object') {
          this.runner.log('System configuration retrieved successfully');
          
          const configFields = Object.keys(systemConfig);
          this.runner.log(`Configuration fields: ${configFields.join(', ')}`);
        }
      } catch (configError) {
        this.runner.log('System configuration endpoint not implemented yet');
      }

      // Test updating system settings
      try {
        const configUpdate = {
          maintenanceMode: false,
          maxFileUploadSize: 10485760, // 10MB
          allowNewRegistrations: true,
          emailNotifications: true
        };

        const updatedConfig = await this.runner.makeRequest('PUT', '/admin/config', configUpdate, 'admin');
        this.runner.log('System configuration updated successfully');
      } catch (updateError) {
        this.runner.log('System configuration update endpoint not implemented yet');
      }

      // Test feature flags
      try {
        const featureFlags = await this.runner.makeRequest('GET', '/admin/features', null, 'admin');
        
        if (featureFlags && typeof featureFlags === 'object') {
          this.runner.log('Feature flags retrieved successfully');
          
          const features = Object.keys(featureFlags);
          this.runner.log(`Available features: ${features.join(', ')}`);
        }
      } catch (featuresError) {
        this.runner.log('Feature flags endpoint not implemented yet');
      }

      // Test updating feature flags
      try {
        const featureUpdate = {
          realTimeMessaging: true,
          advancedAnalytics: true,
          documentVersioning: false
        };

        await this.runner.makeRequest('PUT', '/admin/features', featureUpdate, 'admin');
        this.runner.log('Feature flags updated successfully');
      } catch (featureUpdateError) {
        this.runner.log('Feature flag update endpoint not implemented yet');
      }

      // Test system limits configuration
      try {
        const systemLimits = await this.runner.makeRequest('GET', '/admin/limits', null, 'admin');
        
        if (systemLimits && typeof systemLimits === 'object') {
          this.runner.log('System limits retrieved successfully');
        }
      } catch (limitsError) {
        this.runner.log('System limits endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testContentModeration() {
    this.runner.startTest('Content Moderation', 'Test content moderation and security features');

    try {
      // Test getting flagged content
      try {
        const flaggedContent = await this.runner.makeRequest('GET', '/admin/moderation/flagged', null, 'admin');
        
        if (Array.isArray(flaggedContent)) {
          this.runner.log(`Found ${flaggedContent.length} flagged content items`);
          
          if (flaggedContent.length > 0) {
            const flaggedItem = flaggedContent[0];
            const flagFields = ['_id', 'type', 'content', 'reason', 'reportedBy', 'createdAt'];
            
            for (const field of flagFields) {
              if (!(field in flaggedItem)) {
                this.runner.log(`Warning: Flagged content missing field: ${field}`);
              }
            }
          }
        }
      } catch (flaggedError) {
        this.runner.log('Flagged content endpoint not implemented yet');
      }

      // Test content approval/rejection
      try {
        const flaggedContent = await this.runner.makeRequest('GET', '/admin/moderation/flagged', null, 'admin');
        
        if (flaggedContent && flaggedContent.length > 0) {
          const contentId = flaggedContent[0]._id;
          
          // Test approving content
          const approvalResult = await this.runner.makeRequest('PUT', `/admin/moderation/${contentId}/approve`, { reason: 'Content is appropriate' }, 'admin');
          this.runner.log('Content approved successfully');
        }
      } catch (approvalError) {
        this.runner.log('Content approval endpoint not implemented yet');
      }

      // Test user reports
      try {
        const userReports = await this.runner.makeRequest('GET', '/admin/reports/users', null, 'admin');
        
        if (Array.isArray(userReports)) {
          this.runner.log(`Found ${userReports.length} user reports`);
        }
      } catch (reportsError) {
        this.runner.log('User reports endpoint not implemented yet');
      }

      // Test automated content scanning
      try {
        const scanResults = await this.runner.makeRequest('POST', '/admin/moderation/scan', { type: 'all' }, 'admin');
        this.runner.log('Content scan initiated successfully');
      } catch (scanError) {
        this.runner.log('Content scanning endpoint not implemented yet');
      }

      // Test content filtering rules
      try {
        const filteringRules = await this.runner.makeRequest('GET', '/admin/moderation/rules', null, 'admin');
        
        if (Array.isArray(filteringRules)) {
          this.runner.log(`Found ${filteringRules.length} content filtering rules`);
        }
      } catch (rulesError) {
        this.runner.log('Content filtering rules endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testSecurityManagement() {
    this.runner.startTest('Security Management', 'Test security monitoring and management features');

    try {
      // Test security logs
      try {
        const securityLogs = await this.runner.makeRequest('GET', '/admin/security/logs', null, 'admin');
        
        if (Array.isArray(securityLogs)) {
          this.runner.log(`Found ${securityLogs.length} security log entries`);
          
          if (securityLogs.length > 0) {
            const logEntry = securityLogs[0];
            const logFields = ['timestamp', 'event', 'user', 'ip', 'severity'];
            
            for (const field of logFields) {
              if (!(field in logEntry)) {
                this.runner.log(`Warning: Security log missing field: ${field}`);
              }
            }
          }
        }
      } catch (logsError) {
        this.runner.log('Security logs endpoint not implemented yet');
      }

      // Test failed login attempts
      try {
        const failedLogins = await this.runner.makeRequest('GET', '/admin/security/failed-logins', null, 'admin');
        
        if (Array.isArray(failedLogins)) {
          this.runner.log(`Found ${failedLogins.length} failed login attempts`);
        }
      } catch (failedLoginsError) {
        this.runner.log('Failed logins endpoint not implemented yet');
      }

      // Test IP blocking
      try {
        const blockedIPs = await this.runner.makeRequest('GET', '/admin/security/blocked-ips', null, 'admin');
        
        if (Array.isArray(blockedIPs)) {
          this.runner.log(`Found ${blockedIPs.length} blocked IP addresses`);
        }
      } catch (blockedIPsError) {
        this.runner.log('Blocked IPs endpoint not implemented yet');
      }

      // Test security alerts
      try {
        const securityAlerts = await this.runner.makeRequest('GET', '/admin/security/alerts', null, 'admin');
        
        if (Array.isArray(securityAlerts)) {
          this.runner.log(`Found ${securityAlerts.length} security alerts`);
        }
      } catch (alertsError) {
        this.runner.log('Security alerts endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testSystemMonitoring() {
    this.runner.startTest('System Monitoring', 'Test system health and performance monitoring');

    try {
      // Test system health
      try {
        const systemHealth = await this.runner.makeRequest('GET', '/admin/system/health', null, 'admin');
        
        if (systemHealth && typeof systemHealth === 'object') {
          this.runner.log('System health data retrieved successfully');
          
          const healthFields = Object.keys(systemHealth);
          this.runner.log(`Health metrics: ${healthFields.join(', ')}`);
        }
      } catch (healthError) {
        this.runner.log('System health endpoint not implemented yet');
      }

      // Test performance metrics
      try {
        const performanceMetrics = await this.runner.makeRequest('GET', '/admin/system/performance', null, 'admin');
        
        if (performanceMetrics && typeof performanceMetrics === 'object') {
          this.runner.log('Performance metrics retrieved successfully');
        }
      } catch (performanceError) {
        this.runner.log('Performance metrics endpoint not implemented yet');
      }

      // Test error logs
      try {
        const errorLogs = await this.runner.makeRequest('GET', '/admin/system/errors', null, 'admin');
        
        if (Array.isArray(errorLogs)) {
          this.runner.log(`Found ${errorLogs.length} error log entries`);
        }
      } catch (errorLogsError) {
        this.runner.log('Error logs endpoint not implemented yet');
      }

      // Test system status
      try {
        const systemStatus = await this.runner.makeRequest('GET', '/admin/system/status', null, 'admin');
        
        if (systemStatus && typeof systemStatus === 'object') {
          this.runner.log('System status retrieved successfully');
        }
      } catch (statusError) {
        this.runner.log('System status endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testDataManagement() {
    this.runner.startTest('Data Management', 'Test data backup, export, and maintenance features');

    try {
      // Test data export
      try {
        const exportResult = await this.runner.makeRequest('POST', '/admin/data/export', { 
          type: 'users',
          format: 'json'
        }, 'admin');
        
        if (exportResult && exportResult.exportId) {
          this.runner.log(`Data export initiated with ID: ${exportResult.exportId}`);
        }
      } catch (exportError) {
        this.runner.log('Data export endpoint not implemented yet');
      }

      // Test backup status
      try {
        const backupStatus = await this.runner.makeRequest('GET', '/admin/data/backups', null, 'admin');
        
        if (Array.isArray(backupStatus)) {
          this.runner.log(`Found ${backupStatus.length} backup records`);
        }
      } catch (backupError) {
        this.runner.log('Backup status endpoint not implemented yet');
      }

      // Test data cleanup
      try {
        const cleanupResult = await this.runner.makeRequest('POST', '/admin/data/cleanup', { 
          type: 'old_logs',
          olderThan: '30d'
        }, 'admin');
        
        this.runner.log('Data cleanup initiated successfully');
      } catch (cleanupError) {
        this.runner.log('Data cleanup endpoint not implemented yet');
      }

      // Test database statistics
      try {
        const dbStats = await this.runner.makeRequest('GET', '/admin/data/statistics', null, 'admin');
        
        if (dbStats && typeof dbStats === 'object') {
          this.runner.log('Database statistics retrieved successfully');
        }
      } catch (statsError) {
        this.runner.log('Database statistics endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tests = new AdminPanelTests();
  tests.runAllTests().then(report => {
    console.log('\nAdmin Panel Tests Complete!');
    process.exit(report.summary.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = AdminPanelTests;
