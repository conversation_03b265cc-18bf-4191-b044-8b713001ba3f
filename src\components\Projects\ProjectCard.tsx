import React from 'react';
import { 
  Calendar, 
  DollarSign, 
  Users, 
  Clock, 
  MapPin, 
  Star,
  Eye,
  MessageCircle,
  Bookmark,
  MoreHorizontal
} from 'lucide-react';
import { User } from '../../services/authService';
import { projectService } from '../../services/projectService';

interface Project {
  _id: string;
  title: string;
  description: string;
  budget: number;
  deadline: string;
  status: 'open' | 'in-progress' | 'review' | 'completed' | 'cancelled';
  category: string;
  requirements: string[];
  skills?: string[];
  client: {
    _id: string;
    name: string;
    avatar?: string;
    rating?: number;
    location?: string;
  } | null;
  bids?: any[];
  views?: number;
  createdAt: string;
  isUrgent?: boolean;
  assignedVendor?: User;
}

interface ProjectCardProps {
  project: Project;
  user: User;
  onBidClick?: (projectId: string) => void;
  onViewDetails?: (projectId: string) => void;
}

export default function ProjectCard({
  project,
  user,
  onViewDetails,
  onBidClick
}: ProjectCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-success-100 text-success-700';
      case 'in-progress': return 'bg-warning-100 text-warning-700';
      case 'review': return 'bg-primary-100 text-primary-700';
      case 'completed': return 'bg-secondary-100 text-secondary-700';
      case 'cancelled': return 'bg-error-100 text-error-700';
      default: return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getDaysLeft = (deadline: string) => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysLeft = getDaysLeft(project.deadline);

  return (
    <article className="card p-6 hover:shadow-green transition-all duration-300 group" role="article">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(project.status)}`}>
              {project.status === 'in-progress' ? 'In Progress' :
               project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${projectService.getCategoryColor(project.category)}`}>
              {projectService.getCategories().find(c => c.value === project.category)?.label || project.category}
            </span>
            {project.isUrgent && (
              <span className="px-2 py-1 bg-warning-100 text-warning-700 rounded-full text-xs font-semibold">
                Urgent
              </span>
            )}
          </div>
          <h3 className="text-lg font-display font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
            {project.title}
          </h3>
          <p className="text-secondary-600 text-sm line-clamp-2 leading-relaxed">
            {project.description}
          </p>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          {/* Header actions can go here */}
        </div>
      </div>

      {/* Requirements/Skills */}
      {project.requirements && project.requirements.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {project.requirements.slice(0, 4).map((requirement, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-primary-50 text-primary-600 rounded text-xs font-medium"
            >
              {requirement}
            </span>
          ))}
          {project.requirements.length > 4 && (
            <span className="px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs font-medium">
              +{project.requirements.length - 4} more
            </span>
          )}
        </div>
      )}

      {/* Project Details */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <DollarSign className="w-4 h-4" />
          <span className="font-semibold text-secondary-900">${project.budget.toLocaleString()}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Calendar className="w-4 h-4" />
          <span className="font-semibold text-error-600">
            2 weeks left
          </span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Users className="w-4 h-4" />
          <span>{project.bids?.length || 0} bids</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-secondary-600">
          <Eye className="w-4 h-4" />
          <span>{project.views || 0} views</span>
        </div>
      </div>

      {/* Client Info */}
      <div className="flex items-center justify-between pt-4 border-t border-secondary-100">
        <div className="flex items-center space-x-3">
          {project.client?.avatar ? (
            <img
              src={project.client.avatar}
              alt={project.client.name || 'Unknown Client'}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-primary-600 font-semibold text-sm">
                {(project.client?.name || 'Unknown Client').charAt(0)}
              </span>
            </div>
          )}
          <div>
            <p className="font-medium text-secondary-900 text-sm">{project.client?.name || 'Unknown Client'}</p>
            <div className="flex items-center space-x-2 text-xs text-secondary-500">
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-current text-warning-400" />
                <span>{project.client?.rating || 0}</span>
              </div>
              <span>•</span>
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{project.client?.location || ''}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {user.role === 'vendor' && project.status === 'open' && project.client?._id !== user._id && (
            <button
              onClick={() => onBidClick?.(project._id)}
              className="btn-primary text-sm py-2 px-3"
            >
              Submit Bid
            </button>
          )}
          <button
            onClick={() => onViewDetails?.(project._id)}
            className="btn-secondary text-sm py-2 px-3"
          >
            View Details
          </button>
          {project.client?._id === user._id && (
            <button
              aria-label="Edit project"
              className="p-2 text-secondary-400 hover:text-secondary-600 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Posted Time */}
      <div className="mt-3 text-xs text-secondary-400">
        Posted {new Date(project.createdAt).toLocaleDateString()}
      </div>
    </article>
  );
}
