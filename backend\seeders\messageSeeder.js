import { Message, Conversation } from '../models/messageModel.js';

// Sample conversation messages
const conversationTemplates = [
  {
    type: 'project_inquiry',
    messages: [
      "Hi! I'm interested in your project. Could we discuss the requirements in more detail?",
      "Hello! Thanks for reaching out. I'd be happy to discuss the project with you. What specific aspects would you like to know more about?",
      "Great! I'm particularly interested in the timeline and any specific technologies you prefer.",
      "The timeline is flexible, but we're hoping to complete it within 6-8 weeks. As for technologies, we're open to suggestions based on your expertise.",
      "Perfect! I have extensive experience with the tech stack mentioned. I'll prepare a detailed proposal for you.",
      "That sounds excellent. I look forward to reviewing your proposal."
    ]
  },
  {
    type: 'project_discussion',
    messages: [
      "I've reviewed your bid and I'm impressed with your portfolio. Can we schedule a call to discuss the project?",
      "Thank you! I'm available for a call this week. What time works best for you?",
      "How about Thursday at 2 PM EST?",
      "Thursday at 2 PM EST works perfectly for me. I'll send you a calendar invite.",
      "Great! I'll prepare some questions about the project scope and deliverables.",
      "Perfect. I'll also have the project requirements document ready to share."
    ]
  },
  {
    type: 'project_progress',
    messages: [
      "Hi! Just wanted to give you an update on the project progress. I've completed the initial wireframes.",
      "That's great news! Could you share the wireframes for review?",
      "Absolutely! I've uploaded them to the project documents. Please take a look and let me know your thoughts.",
      "I've reviewed the wireframes and they look fantastic! Just a few minor adjustments needed.",
      "Thanks for the feedback! I'll make those adjustments and have the updated version ready by tomorrow.",
      "Perfect! Once those are done, we can move to the next phase."
    ]
  },
  {
    type: 'technical_discussion',
    messages: [
      "I have a question about the API integration requirements. Which payment gateway would you prefer?",
      "We typically use Stripe for our payment processing. Do you have experience with their API?",
      "Yes, I've integrated Stripe many times. It's a great choice. Should I include webhook handling for payment confirmations?",
      "Yes, webhook handling would be essential for our workflow. Also, we'll need to handle subscription payments.",
      "No problem! I'll include both one-time and subscription payment handling in the implementation.",
      "Excellent! That covers all our payment requirements."
    ]
  },
  {
    type: 'project_completion',
    messages: [
      "The project is now complete! I've deployed it to the staging environment for your review.",
      "Wonderful! I'll test it thoroughly and provide feedback by end of day.",
      "I've tested everything and it works perfectly! The quality exceeds our expectations.",
      "Thank you so much! I'm glad you're happy with the results. I'll prepare the final deliverables.",
      "Could you also provide documentation for future maintenance?",
      "Of course! I'll include comprehensive documentation and a brief training session if needed."
    ]
  }
];

// Seed conversations and messages
export const seedConversationsAndMessages = async (users, projects) => {
  try {
    const clients = users.filter(user => user.role === 'client');
    const vendors = users.filter(user => user.role === 'vendor');
    const conversations = [];
    const messages = [];
    
    // Create conversations between clients and vendors
    for (let i = 0; i < Math.min(clients.length * 2, 10); i++) {
      const client = clients[i % clients.length];
      const vendor = vendors[i % vendors.length];
      const project = projects[i % projects.length];
      const template = conversationTemplates[i % conversationTemplates.length];
      
      // Create conversation
      const conversation = {
        participants: [client._id, vendor._id],
        project: project._id,
        title: `Discussion about: ${project.title}`,
        type: 'project',
        isActive: true,
        unreadCount: new Map(),
        metadata: {
          projectTitle: project.title,
          clientName: client.name,
          vendorName: vendor.name
        }
      };
      
      const createdConversation = await Conversation.create(conversation);
      conversations.push(createdConversation);
      
      // Create messages for this conversation
      const conversationMessages = [];
      for (let j = 0; j < template.messages.length; j++) {
        const isClientMessage = j % 2 === 0;
        const sender = isClientMessage ? client._id : vendor._id;
        
        const message = {
          conversation: createdConversation._id,
          sender: sender,
          content: template.messages[j],
          attachments: [],
          readBy: [
            {
              user: sender,
              readAt: new Date(Date.now() - (template.messages.length - j) * 60 * 60 * 1000)
            }
          ],
          deletedFor: [],
          metadata: {
            messageType: template.type,
            projectId: project._id.toString()
          }
        };
        
        // Set message timestamp (spread over last few days)
        const messageDate = new Date(Date.now() - (template.messages.length - j) * 60 * 60 * 1000);
        message.createdAt = messageDate;
        message.updatedAt = messageDate;
        
        conversationMessages.push(message);
      }
      
      const createdMessages = await Message.insertMany(conversationMessages);
      messages.push(...createdMessages);
      
      // Update conversation with last message
      if (createdMessages.length > 0) {
        const lastMessage = createdMessages[createdMessages.length - 1];
        await Conversation.findByIdAndUpdate(createdConversation._id, {
          lastMessage: lastMessage._id,
          updatedAt: lastMessage.createdAt
        });
      }
    }
    
    // Create some direct conversations (not project-related)
    for (let i = 0; i < 3; i++) {
      const user1 = users[i * 2];
      const user2 = users[i * 2 + 1];
      
      const conversation = {
        participants: [user1._id, user2._id],
        title: `${user1.name} & ${user2.name}`,
        type: 'direct',
        isActive: true,
        unreadCount: new Map(),
        metadata: {}
      };
      
      const createdConversation = await Conversation.create(conversation);
      conversations.push(createdConversation);
      
      // Add a few messages
      const directMessages = [
        `Hi ${user2.name}! Hope you're doing well.`,
        `Hello ${user1.name}! Thanks for reaching out. How can I help you?`,
        "I was wondering if you'd be interested in collaborating on future projects.",
        "That sounds interesting! I'd love to learn more about what you have in mind."
      ];
      
      const conversationMessages = [];
      for (let j = 0; j < directMessages.length; j++) {
        const sender = j % 2 === 0 ? user1._id : user2._id;
        
        const message = {
          conversation: createdConversation._id,
          sender: sender,
          content: directMessages[j],
          attachments: [],
          readBy: [
            {
              user: sender,
              readAt: new Date(Date.now() - (directMessages.length - j) * 2 * 60 * 60 * 1000)
            }
          ],
          deletedFor: [],
          metadata: {
            messageType: 'direct'
          }
        };
        
        const messageDate = new Date(Date.now() - (directMessages.length - j) * 2 * 60 * 60 * 1000);
        message.createdAt = messageDate;
        message.updatedAt = messageDate;
        
        conversationMessages.push(message);
      }
      
      const createdMessages = await Message.insertMany(conversationMessages);
      messages.push(...createdMessages);
      
      // Update conversation with last message
      if (createdMessages.length > 0) {
        const lastMessage = createdMessages[createdMessages.length - 1];
        await Conversation.findByIdAndUpdate(createdConversation._id, {
          lastMessage: lastMessage._id,
          updatedAt: lastMessage.createdAt
        });
      }
    }
    
    console.log(`✅ Created ${conversations.length} conversations and ${messages.length} messages`);
    return { conversations, messages };
  } catch (error) {
    console.error('❌ Error seeding conversations and messages:', error);
    return { conversations: [], messages: [] };
  }
};
