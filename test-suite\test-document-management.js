#!/usr/bin/env node

/**
 * Document Management Feature Tests
 * Tests all document-related functionality
 */

const { TestRunner, DEMO_USERS, BASE_URL } = require('./comprehensive-feature-test');

class DocumentManagementTests {
  constructor() {
    this.runner = new TestRunner();
  }

  async runAllTests() {
    console.log('\n📁 Starting Document Management Tests...\n');

    try {
      // Authenticate users
      await this.runner.authenticate('clients', 0); // <PERSON>
      await this.runner.authenticate('clients', 1); // <PERSON>
      await this.runner.authenticate('vendors', 0); // <PERSON>
      await this.runner.authenticate('vendors', 1); // <PERSON>
      await this.runner.authenticate('admin');

      // Run all document management tests
      await this.testViewDocuments();
      await this.testUploadDocument();
      await this.testSecurityLevels();
      await this.testVersionControl();
      await this.testRoleBasedAccess();
      await this.testDocumentTypes();
      await this.testDocumentSearch();
      await this.testDocumentSharing();

    } catch (error) {
      console.error('Document management test suite failed:', error);
    }

    return this.runner.generateReport();
  }

  async testViewDocuments() {
    this.runner.startTest('View Documents', 'Test viewing 30+ demo documents across file types');

    try {
      // Get all documents
      const allDocuments = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      
      if (!Array.isArray(allDocuments)) {
        throw new Error('Documents response is not an array');
      }

      this.runner.log(`Found ${allDocuments.length} total documents in system`);

      // Verify we have at least 30 documents
      if (allDocuments.length < 30) {
        this.runner.log(`Warning: Expected at least 30 documents, found ${allDocuments.length}`);
      }

      // Test document structure
      if (allDocuments.length > 0) {
        const sampleDocument = allDocuments[0];
        const requiredFields = ['_id', 'name', 'type', 'size', 'uploadedBy', 'createdAt'];
        
        for (const field of requiredFields) {
          if (!(field in sampleDocument)) {
            throw new Error(`Document missing required field: ${field}`);
          }
        }

        this.runner.log('Document structure validation passed');
      }

      // Test file type diversity
      const fileTypes = [...new Set(allDocuments.map(d => d.type))];
      this.runner.log(`Found ${fileTypes.length} different file types: ${fileTypes.join(', ')}`);

      if (fileTypes.length < 5) {
        this.runner.log(`Warning: Expected at least 5 different file types, found ${fileTypes.length}`);
      }

      // Test vendor view
      const vendorDocuments = await this.runner.makeRequest('GET', '/documents', null, 'vendors', 0);
      this.runner.log(`Vendor can see ${vendorDocuments.length} documents`);

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testUploadDocument() {
    this.runner.startTest('Upload Document', 'Test uploading documents with metadata');

    try {
      // Test document upload (simulated)
      const newDocument = {
        name: 'project-specification.pdf',
        type: 'application/pdf',
        size: 2048576,
        description: 'Detailed project specifications and requirements',
        category: 'requirements',
        securityLevel: 'private',
        tags: ['specification', 'requirements', 'project']
      };

      // Note: Actual file upload would require multipart/form-data
      // This tests the metadata creation
      try {
        const uploadedDocument = await this.runner.makeRequest('POST', '/documents', newDocument, 'clients', 0);
        
        if (uploadedDocument._id) {
          this.runner.log(`Document uploaded successfully with ID: ${uploadedDocument._id}`);
          
          // Verify document was created correctly
          const fetchedDocument = await this.runner.makeRequest('GET', `/documents/${uploadedDocument._id}`, null, 'clients', 0);
          
          if (fetchedDocument.name !== newDocument.name) {
            throw new Error('Document name does not match');
          }

          if (fetchedDocument.securityLevel !== newDocument.securityLevel) {
            throw new Error('Document security level does not match');
          }

          this.runner.log('Document upload and verification successful');
        }
      } catch (uploadError) {
        this.runner.log('Document upload endpoint may not be fully implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testSecurityLevels() {
    this.runner.startTest('Security Levels', 'Test document security levels and access control');

    try {
      // Get documents with different security levels
      const documents = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      
      // Check security level distribution
      const securityLevels = {};
      documents.forEach(doc => {
        if (doc.securityLevel) {
          securityLevels[doc.securityLevel] = (securityLevels[doc.securityLevel] || 0) + 1;
        }
      });

      this.runner.log(`Security level distribution: ${JSON.stringify(securityLevels)}`);

      // Test creating documents with different security levels
      const securityTestLevels = ['public', 'private', 'confidential'];
      
      for (const level of securityTestLevels) {
        try {
          const secureDocument = {
            name: `test-${level}-document.txt`,
            type: 'text/plain',
            size: 1024,
            description: `Test document with ${level} security level`,
            securityLevel: level
          };

          const createdDoc = await this.runner.makeRequest('POST', '/documents', secureDocument, 'clients', 0);
          
          if (createdDoc._id) {
            this.runner.log(`Created ${level} document successfully`);
          }
        } catch (securityError) {
          this.runner.log(`Security level ${level} creation failed or not implemented`);
        }
      }

      // Test access restrictions
      try {
        const privateDocuments = documents.filter(d => d.securityLevel === 'private');
        
        if (privateDocuments.length > 0) {
          const privateDocId = privateDocuments[0]._id;
          
          // Test vendor access to private document
          try {
            await this.runner.makeRequest('GET', `/documents/${privateDocId}`, null, 'vendors', 0);
            this.runner.log('Warning: Vendor can access private document - check permissions');
          } catch (accessError) {
            this.runner.log('Private document access correctly restricted for vendor');
          }
        }
      } catch (accessTestError) {
        this.runner.log('Access restriction testing failed');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testVersionControl() {
    this.runner.startTest('Version Control', 'Test document version control and change tracking');

    try {
      // Get a document to test versioning
      const documents = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      
      if (documents.length === 0) {
        this.runner.log('No documents available for version control test');
        this.runner.endTest(true);
        return;
      }

      const documentId = documents[0]._id;

      // Test creating a new version
      const versionData = {
        name: documents[0].name,
        type: documents[0].type,
        size: documents[0].size + 1024,
        description: 'Updated version with additional content',
        versionNote: 'Added new requirements section'
      };

      try {
        const newVersion = await this.runner.makeRequest('POST', `/documents/${documentId}/versions`, versionData, 'clients', 0);
        
        if (newVersion._id || newVersion.version) {
          this.runner.log('New document version created successfully');
        }
      } catch (versionError) {
        this.runner.log('Document versioning endpoint not implemented yet');
      }

      // Test version history
      try {
        const versionHistory = await this.runner.makeRequest('GET', `/documents/${documentId}/versions`, null, 'clients', 0);
        
        if (Array.isArray(versionHistory)) {
          this.runner.log(`Document has ${versionHistory.length} versions`);
          
          // Check version structure
          if (versionHistory.length > 0) {
            const version = versionHistory[0];
            const versionFields = ['version', 'createdAt', 'createdBy'];
            
            for (const field of versionFields) {
              if (!(field in version)) {
                this.runner.log(`Warning: Version missing field: ${field}`);
              }
            }
          }
        }
      } catch (historyError) {
        this.runner.log('Version history endpoint not implemented yet');
      }

      // Test version comparison
      try {
        const versions = await this.runner.makeRequest('GET', `/documents/${documentId}/versions`, null, 'clients', 0);
        
        if (versions && versions.length >= 2) {
          const comparison = await this.runner.makeRequest('GET', `/documents/${documentId}/versions/compare?v1=${versions[0].version}&v2=${versions[1].version}`, null, 'clients', 0);
          this.runner.log('Version comparison retrieved successfully');
        }
      } catch (compareError) {
        this.runner.log('Version comparison endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testRoleBasedAccess() {
    this.runner.startTest('Role-based Access', 'Test role-based document access permissions');

    try {
      // Test admin access
      const adminDocuments = await this.runner.makeRequest('GET', '/documents', null, 'admin');
      this.runner.log(`Admin can see ${adminDocuments.length} documents`);

      // Test client access
      const clientDocuments = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      this.runner.log(`Client can see ${clientDocuments.length} documents`);

      // Test vendor access
      const vendorDocuments = await this.runner.makeRequest('GET', '/documents', null, 'vendors', 0);
      this.runner.log(`Vendor can see ${vendorDocuments.length} documents`);

      // Test document permissions endpoint
      if (clientDocuments.length > 0) {
        const documentId = clientDocuments[0]._id;
        
        try {
          const permissions = await this.runner.makeRequest('GET', `/documents/${documentId}/permissions`, null, 'clients', 0);
          
          if (permissions && typeof permissions === 'object') {
            this.runner.log(`Document permissions: ${JSON.stringify(permissions)}`);
          }
        } catch (permError) {
          this.runner.log('Document permissions endpoint not implemented yet');
        }

        // Test updating permissions
        try {
          const newPermissions = {
            read: ['client', 'vendor'],
            write: ['client'],
            delete: ['client']
          };

          await this.runner.makeRequest('PUT', `/documents/${documentId}/permissions`, newPermissions, 'clients', 0);
          this.runner.log('Document permissions updated successfully');
        } catch (updatePermError) {
          this.runner.log('Permission update endpoint not implemented yet');
        }
      }

      // Test project-specific document access
      try {
        const projects = await this.runner.makeRequest('GET', '/projects', null, 'clients', 0);
        
        if (projects.length > 0) {
          const projectId = projects[0]._id;
          const projectDocuments = await this.runner.makeRequest('GET', `/projects/${projectId}/documents`, null, 'clients', 0);
          
          this.runner.log(`Project ${projectId} has ${projectDocuments.length} documents`);
        }
      } catch (projectDocError) {
        this.runner.log('Project document access endpoint not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testDocumentTypes() {
    this.runner.startTest('Document Types', 'Test handling of different document file types');

    try {
      const documents = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      
      // Analyze file types
      const typeAnalysis = {};
      documents.forEach(doc => {
        const type = doc.type || 'unknown';
        typeAnalysis[type] = (typeAnalysis[type] || 0) + 1;
      });

      this.runner.log(`File type analysis: ${JSON.stringify(typeAnalysis, null, 2)}`);

      // Expected file types for a comprehensive demo
      const expectedTypes = [
        'application/pdf',
        'image/png',
        'image/jpeg',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/zip'
      ];

      const foundTypes = Object.keys(typeAnalysis);
      const missingTypes = expectedTypes.filter(type => !foundTypes.includes(type));
      
      if (missingTypes.length > 0) {
        this.runner.log(`Missing file types: ${missingTypes.join(', ')}`);
      }

      // Test file type filtering
      for (const type of foundTypes.slice(0, 3)) { // Test first 3 types
        try {
          const filteredDocs = await this.runner.makeRequest('GET', `/documents?type=${encodeURIComponent(type)}`, null, 'clients', 0);
          
          if (Array.isArray(filteredDocs)) {
            const allMatchType = filteredDocs.every(d => d.type === type);
            
            if (allMatchType) {
              this.runner.log(`Type filter '${type}' working correctly (${filteredDocs.length} documents)`);
            } else {
              throw new Error(`Type filter '${type}' returned incorrect results`);
            }
          }
        } catch (filterError) {
          this.runner.log(`Type filtering for '${type}' not implemented or failed`);
        }
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testDocumentSearch() {
    this.runner.startTest('Document Search', 'Test document search and filtering capabilities');

    try {
      // Test search functionality
      const searchTerms = ['specification', 'design', 'contract'];
      
      for (const term of searchTerms) {
        try {
          const searchResults = await this.runner.makeRequest('GET', `/documents?search=${term}`, null, 'clients', 0);
          
          if (Array.isArray(searchResults)) {
            this.runner.log(`Search for '${term}' returned ${searchResults.length} results`);
            
            // Verify search results contain the search term
            const relevantResults = searchResults.filter(d => 
              d.name.toLowerCase().includes(term.toLowerCase()) ||
              (d.description && d.description.toLowerCase().includes(term.toLowerCase()))
            );
            
            this.runner.log(`${relevantResults.length} results are relevant to search term '${term}'`);
          }
        } catch (searchError) {
          this.runner.log(`Document search for '${term}' not implemented or failed`);
        }
      }

      // Test tag-based search
      try {
        const taggedDocuments = await this.runner.makeRequest('GET', '/documents?tags=requirements', null, 'clients', 0);
        
        if (Array.isArray(taggedDocuments)) {
          this.runner.log(`Found ${taggedDocuments.length} documents with 'requirements' tag`);
        }
      } catch (tagSearchError) {
        this.runner.log('Tag-based search not implemented yet');
      }

      // Test date range filtering
      try {
        const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
        const recentDocuments = await this.runner.makeRequest('GET', `/documents?since=${oneMonthAgo}`, null, 'clients', 0);
        
        if (Array.isArray(recentDocuments)) {
          this.runner.log(`Found ${recentDocuments.length} documents from the last month`);
        }
      } catch (dateFilterError) {
        this.runner.log('Date filtering not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }

  async testDocumentSharing() {
    this.runner.startTest('Document Sharing', 'Test document sharing and collaboration features');

    try {
      const documents = await this.runner.makeRequest('GET', '/documents', null, 'clients', 0);
      
      if (documents.length === 0) {
        this.runner.log('No documents available for sharing test');
        this.runner.endTest(true);
        return;
      }

      const documentId = documents[0]._id;

      // Test sharing document with specific users
      const sharingData = {
        users: [DEMO_USERS.vendors[0].email, DEMO_USERS.vendors[1].email],
        permissions: ['read', 'comment'],
        message: 'Please review this document and provide feedback'
      };

      try {
        const shareResult = await this.runner.makeRequest('POST', `/documents/${documentId}/share`, sharingData, 'clients', 0);
        this.runner.log('Document shared successfully');
      } catch (shareError) {
        this.runner.log('Document sharing endpoint not implemented yet');
      }

      // Test generating shareable link
      try {
        const shareLink = await this.runner.makeRequest('POST', `/documents/${documentId}/share-link`, { expiresIn: '7d' }, 'clients', 0);
        
        if (shareLink && shareLink.url) {
          this.runner.log(`Shareable link generated: ${shareLink.url}`);
        }
      } catch (linkError) {
        this.runner.log('Shareable link generation not implemented yet');
      }

      // Test document comments/annotations
      try {
        const comment = {
          content: 'This section needs clarification',
          page: 1,
          position: { x: 100, y: 200 }
        };

        const addedComment = await this.runner.makeRequest('POST', `/documents/${documentId}/comments`, comment, 'vendors', 0);
        this.runner.log('Document comment added successfully');
      } catch (commentError) {
        this.runner.log('Document commenting not implemented yet');
      }

      this.runner.endTest(true);
    } catch (error) {
      this.runner.endTest(false, error.message);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tests = new DocumentManagementTests();
  tests.runAllTests().then(report => {
    console.log('\nDocument Management Tests Complete!');
    process.exit(report.summary.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = DocumentManagementTests;
